# Claude as Agent

A project to set up <PERSON> as a general-purpose agent with powerful capabilities and proper boundaries.

## Overview

This project configures Claude Code to act as an intelligent agent capable of:
- File system operations (read, write, edit)
- Shell command execution
- Web searches and content fetching
- Code analysis and generation
- Task planning and execution

While maintaining security boundaries:
- Restricted directory access
- Defensive security practices only
- No malicious code generation or execution

## Prerequisites

- Python 3.10 or higher
- Conda package manager
- <PERSON> CLI installed

## Setup

1. Clone this repository:
   ```bash
   git clone <repository-url>
   cd claude-as-agent
   ```

2. Create and activate the conda environment:
   ```bash
   conda env create -f environment.yml
   conda activate claude-agent
   ```

3. Configure Claude Code:
   - Review `CLAUDE.md` for agent-specific instructions
   - Adjust access permissions as needed

## Usage

Run Claude Code in the project directory:
```bash
claude
```

<PERSON> will follow the instructions in `CLAUDE.md` and respect the configured boundaries.

## Project Structure

```
claude-as-agent/
├── README.md          # This file
├── CLAUDE.md          # Agent instructions and boundaries
├── environment.yml    # Conda environment configuration
├── .gitignore         # Git ignore patterns
├── tmp/               # Temporary storage
├── workflows/         # Documented task workflows
├── knowledge/         # Best practices and learnings
├── scripts/           # Reusable code and automation
└── templates/         # Templates for common tasks
```

## Continuous Improvement

This project implements a self-improving agent system. Claude actively learns and optimizes its performance through:

### Feedback Loop
- After each task, Claude solicits feedback to improve future performance
- Learnings are documented and stored in the project repository
- Workflows and scripts are created to automate repetitive tasks

### Knowledge Building
- **workflows/**: Contains documented procedures for common tasks
- **knowledge/**: Stores best practices and lessons learned
- **scripts/**: Houses reusable code snippets and automation tools
- **templates/**: Provides starting points foTopic:  AI-Driven Multi-Sided Marketplace Optimization
 
Time: July 27 2025, Sunday 8:30 PM EDT
Meeting ID: 926 7428 2090
Passcode: 112233

Abstract: Multi-sided marketplace business is ubiquitous in our day-to-day life, from e-commerce, ride sharing, food delivery to advertising. Over the past decade, the efficiency and effectiveness of operating such marketplaces have been greatly improved due to innovations in AI, not only because of the advent of new technology but novel applications at large scale. In this talk we will examine several representative marketplace optimization problems and explore how techniques in machine learning, data science, operations research, economics and econometrics can be leveraged to uplevel the marketplace optimization.
  
Speaker：Dr. Yunxiao Liu, Reddit
Speaker bio: Dr. Yunxiao Liu is currently a Senior Machine Learning Engineering Manager at Reddit, where he leads the Ads Marketplace team. Prior to that he was a Machine Learning Engineering Manager at Twitter, and Applied Scientist at Uber. His professional career has focused on multi-sided marketplace optimization, machine learning, data science and advertising. Dr. Liu received his PhD in Statistics and Operations Research, and Master of Economics from the University of North Carolina at Chapel Hill.r frequent operations

### Early Stage Development
As this project is in its early stages, the continuous improvement process is designed to:
- Evolve organically based on real task experiences
- Build efficiency tools as patterns emerge
- Create a personalized knowledge base over time
- Adapt to user preferences and project needs

## Security Considerations

- Claude is configured for defensive security tasks only
- Directory access is restricted to the project workspace
- No creation or modification of potentially malicious code
- All operations are logged and traceable

## Contributing

Please ensure all contributions maintain the security boundaries and agent capabilities defined in this project.

## License

[Specify your license here]