# Model Context Protocol (MCP) - Simple Explanation

## What is MCP?

MCP (Model Context Protocol) is a protocol that allows AI assistants like <PERSON> to interact with external tools and services. Think of it as a bridge that lets <PERSON> use specialized tools beyond its built-in capabilities.

## How Does It Work?

### The Simplest MCP Server

In the simplest case, your MCP server is just a single script that <PERSON> starts directly:

```
┌─────────────┐         ┌─────────────────────┐
│   Claude    │ ◄─────► │    MCP Server       │
│ (AI Model)  │  stdio  │   (Your Script)     │
└─────────────┘         └─────────────────────┘
     User                  Auto-started by
   <PERSON><PERSON> when needed
```

The "MCP Client" and "MCP Server" are often the same thing - a single script that:
- <PERSON> starts when it needs your tool
- Communicates with <PERSON> via stdio (standard input/output)
- Implements the MCP protocol
- Executes your tool functions

### Communication Flow

1. **User asks <PERSON> to do something**
   ```
   User: "Can you check the weather?"
   ```

2. **<PERSON> recognizes it needs a tool**
   - <PERSON> checks available MCP tools
   - Finds a "get_weather" tool

3. **<PERSON> calls the tool**
   ```
   <PERSON> → MCP Client: "Call get_weather for San Francisco"
   ```

4. **MCP Server processes the request**
   ```
   MCP Server: Fetches weather data from API
   MCP Server → <PERSON>: "72°F, <PERSON>"
   ```

5. **<PERSON> responds to user**
   ```
   <PERSON> → User: "The weather in San Francisco is 72°F and sunny."
   ```

## Core Components

### 1. MCP Server (Your Tool)
A program that:
- Implements the MCP protocol
- Defines available tools
- Executes tool functions
- Returns results

### 2. Tool Definitions
Each tool has:
- **Name**: `get_weather`
- **Description**: "Get current weather for a city"
- **Input Schema**: What parameters it needs
- **Function**: The actual code that runs

### 3. Communication Protocol
- Uses **stdio** (standard input/output) by default
- Messages are in **JSON-RPC** format
- Request → Response pattern

## Minimal MCP Server Example

```javascript
// 1. Define available tools
const tools = [{
  name: "add_numbers",
  description: "Add two numbers together",
  parameters: {
    a: { type: "number", description: "First number" },
    b: { type: "number", description: "Second number" }
  }
}];

// 2. Handle tool execution
function executeTool(toolName, args) {
  if (toolName === "add_numbers") {
    return { result: args.a + args.b };
  }
}

// 3. Set up MCP communication
// - Listen for requests on stdin
// - Send responses on stdout
// - Follow JSON-RPC format
```

## Configuration

To use an MCP server, you need to tell Claude about it:

```json
{
  "mcpServers": {
    "my-calculator": {
      "command": "node",
      "args": ["path/to/my-calculator-mcp.js"]
    }
  }
}
```

This configuration tells Claude:
- There's an MCP server called "my-calculator"
- Start it by running `node path/to/my-calculator-mcp.js`
- Communicate with it via stdio

## Key Concepts

1. **Stateless**: Each request is independent
2. **Synchronous**: Request → Response pattern
3. **Tool-focused**: MCP servers provide specific capabilities
4. **Language agnostic**: Can be written in any language

## Why Use MCP?

- **Extend Claude's abilities**: Add custom tools and integrations
- **Keep data local**: Process sensitive data without sending it elsewhere
- **Custom workflows**: Build tools specific to your needs
- **Reusable**: Same tool can work with different AI assistants

## Simple vs Complex MCP Servers

### Simple (Direct) - Most Common
```
Claude ←→ MCP Server (single script)
```
- **One script handles everything**
- Claude starts it when needed
- Perfect for most use cases
- Examples: Calculator, file reader, API caller

### Complex (Separated)
```
Claude ←→ MCP Client ←→ Backend Server ←→ External Service
```
- **MCP client**: Lightweight script Claude starts
- **Backend server**: Separate always-running service
- Needed when:
  - You need persistent state/connections
  - Multiple Claude sessions share resources
  - Complex infrastructure (databases, queues)
- Example: Browser automation, database access

**Most MCP servers are the simple type** - just one script that Claude runs!

## Getting Started

1. **Choose a language**: JavaScript/TypeScript is common
2. **Install MCP SDK**: Provides protocol handling
3. **Define your tools**: What capabilities to add
4. **Implement functions**: Write the actual logic
5. **Configure Claude**: Add to `.mcp.json` or Claude's config
6. **Test**: Try calling your tools from Claude

The beauty of MCP is its simplicity - it's just a standardized way for AI assistants to call functions and get results back!