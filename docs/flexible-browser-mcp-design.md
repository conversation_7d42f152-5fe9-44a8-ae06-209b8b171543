# Flexible Browser MCP Tool Design

## Overview

A powerful MCP tool that allows <PERSON> to:
- Execute arbitrary JavaScript code in any webpage
- Capture screenshots
- Extract DOM content
- Interact with multiple websites
- Return rich data (images, HTML, JSON)

## Architecture

```
┌─────────────┐     MCP      ┌─────────────┐     HTTP     ┌─────────────┐
│   Claude    │ ◄──────────► │ MCP Client  │ ◄──────────► │   Backend   │
│             │    stdio     │             │              │   Server    │
└─────────────┘              └─────────────┘              └──────┬──────┘
                                                                 │
                                                             WebSocket
                                                                 │
                                                          ┌──────▼──────┐
                                                          │   Browser   │
                                                          │  Extension  │
                                                          └──────┬──────┘
                                                                 │
                                                          Executes JS on
                                                             any site
```

## MCP Tool Definitions

### 1. Execute JavaScript
```json
{
  "name": "browser_execute",
  "description": "Execute JavaScript code in the browser on any webpage",
  "inputSchema": {
    "type": "object",
    "properties": {
      "code": {
        "type": "string",
        "description": "JavaScript code to execute"
      },
      "url": {
        "type": "string",
        "description": "URL to navigate to (optional, uses current page if not provided)"
      },
      "targetSite": {
        "type": "string",
        "description": "Site identifier for site-specific behavior (optional)"
      },
      "timeout": {
        "type": "number",
        "description": "Execution timeout in milliseconds",
        "default": 30000
      }
    },
    "required": ["code"]
  }
}
```

### 2. Capture DOM Content
```json
{
  "name": "browser_capture_dom",
  "description": "Capture DOM content and metadata from the current page",
  "inputSchema": {
    "type": "object",
    "properties": {
      "selector": {
        "type": "string",
        "description": "CSS selector to capture specific element (optional, captures full page if not provided)"
      },
      "includeMetadata": {
        "type": "boolean",
        "description": "Include page metadata (URL, title, etc.)",
        "default": true
      }
    }
  }
}
```

### 3. Capture Screenshot
```json
{
  "name": "browser_capture_screenshot",
  "description": "Capture a screenshot of the current page or specific element",
  "inputSchema": {
    "type": "object",
    "properties": {
      "selector": {
        "type": "string",
        "description": "CSS selector to capture specific element (optional)"
      },
      "fullPage": {
        "type": "boolean",
        "description": "Capture full scrollable page",
        "default": false
      },
      "quality": {
        "type": "number",
        "description": "Image quality (1-100)",
        "default": 100
      }
    }
  }
}
```

### 4. Multi-Site Navigation
```json
{
  "name": "browser_navigate",
  "description": "Navigate to a URL and wait for it to load",
  "inputSchema": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string",
        "description": "URL to navigate to"
      },
      "waitFor": {
        "type": "string",
        "description": "CSS selector or JS condition to wait for",
        "default": "document.readyState === 'complete'"
      }
    },
    "required": ["url"]
  }
}
```

## Backend Server Design

### Request/Response Format

```typescript
interface BrowserRequest {
  id: string;
  type: 'execute' | 'capture' | 'navigate';
  data: {
    code?: string;
    url?: string;
    targetSite?: string;
    returnScreenshot?: boolean;
    returnDOM?: boolean;
    selector?: string;
    // ... other parameters
  };
}

interface BrowserResponse {
  id: string;
  success: boolean;
  result?: {
    // JS execution result
    value?: any;
    
    // Screenshot data (base64)
    screenshot?: string;
    
    // DOM content
    dom?: {
      body: string;
      title: string;
      url: string;
    };
    
    // Execution metadata
    metadata?: {
      executionTime: number;
      currentUrl: string;
      pageTitle: string;
    };
  };
  error?: string;
}
```

### Security Considerations

```typescript
// Backend server should implement:
interface SecurityConfig {
  // Allowed domains for navigation
  allowedDomains?: string[];
  
  // Blocked domains
  blockedDomains?: string[];
  
  // Maximum execution time
  maxExecutionTime: number;
  
  // Rate limiting
  rateLimits: {
    requestsPerMinute: number;
    executionsPerMinute: number;
  };
  
  // Code validation
  codeValidation: {
    maxLength: number;
    blockedPatterns: RegExp[];
  };
}
```

## Browser Extension Design

### Content Script

```javascript
// Message handler
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  switch (request.type) {
    case 'execute':
      const result = await executeCode(request.data);
      sendResponse(result);
      break;
      
    case 'capture':
      const pageData = await capturePage(request.data);
      sendResponse(pageData);
      break;
      
    case 'navigate':
      await navigateToUrl(request.data);
      sendResponse({ success: true });
      break;
  }
  return true; // Keep channel open for async response
});

// Execute arbitrary JavaScript
async function executeCode({ code, returnScreenshot, returnDOM }) {
  try {
    // Execute in page context
    const result = await chrome.runtime.sendMessage({
      type: 'executeInPage',
      code: code
    });
    
    const response = { value: result };
    
    // Capture screenshot if requested
    if (returnScreenshot) {
      response.screenshot = await captureScreenshot();
    }
    
    // Capture DOM if requested
    if (returnDOM) {
      response.dom = {
        body: document.body.innerHTML,
        title: document.title,
        url: window.location.href
      };
    }
    
    return { success: true, result: response };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Screenshot capture
async function captureScreenshot(selector) {
  if (selector) {
    // Capture specific element
    const element = document.querySelector(selector);
    if (!element) throw new Error('Element not found');
    
    // Use html2canvas or similar for element capture
    return await captureElement(element);
  } else {
    // Full page screenshot via Chrome API
    return await chrome.tabs.captureVisibleTab(null, {
      format: 'png',
      quality: 100
    });
  }
}
```

### Background Script

```javascript
// Execute code in page context
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'executeInPage') {
    chrome.tabs.executeScript(sender.tab.id, {
      code: request.code,
      allFrames: false
    }, (results) => {
      if (chrome.runtime.lastError) {
        sendResponse({ error: chrome.runtime.lastError.message });
      } else {
        sendResponse({ result: results[0] });
      }
    });
    return true;
  }
});
```

## Site-Specific Configurations

```javascript
// Site configurations for common platforms
const siteConfigs = {
  linkedin: {
    waitForSelectors: {
      feed: '.feed-identity-module',
      postButton: '.share-box-feed-entry__trigger',
      postTextarea: '.ql-editor'
    },
    customActions: {
      ensureLoggedIn: async () => {
        // Check login state
      }
    }
  },
  
  twitter: {
    waitForSelectors: {
      compose: '[data-testid="tweetTextarea_0"]',
      tweetButton: '[data-testid="tweetButton"]'
    }
  },
  
  github: {
    waitForSelectors: {
      repository: '.repository-content',
      issueForm: '.new_issue'
    }
  }
};
```

## Usage Examples

### 1. Complex LinkedIn Automation
```javascript
// Claude can generate and execute this code
const code = `
  // Wait for feed to load
  await waitForSelector('.feed-identity-module');
  
  // Click share button
  document.querySelector('.share-box-feed-entry__trigger').click();
  
  // Wait for editor
  await waitForSelector('.ql-editor');
  
  // Type content
  const editor = document.querySelector('.ql-editor');
  editor.focus();
  editor.innerHTML = '<p>Automated post content</p>';
  
  // Find and click post button
  const postButton = Array.from(document.querySelectorAll('button'))
    .find(btn => btn.textContent.includes('Post'));
  postButton.click();
  
  return { posted: true, timestamp: new Date().toISOString() };
`;

const result = await mcpClient.call('browser_execute', {
  code,
  url: 'https://linkedin.com/feed',
  returnScreenshot: true,
  targetSite: 'linkedin'
});
```

### 2. Data Extraction
```javascript
const code = `
  // Extract all article headlines
  const headlines = Array.from(document.querySelectorAll('h2.article-title'))
    .map(h => ({ 
      text: h.textContent.trim(), 
      link: h.querySelector('a')?.href 
    }));
  
  return { 
    headlines, 
    count: headlines.length,
    timestamp: new Date().toISOString()
  };
`;

const result = await mcpClient.call('browser_execute', {
  code,
  returnDOM: true
});
```

### 3. Visual Verification
```javascript
// Fill a form and capture result
const code = `
  document.querySelector('#email').value = '<EMAIL>';
  document.querySelector('#submit').click();
  await waitForNavigation();
`;

const result = await mcpClient.call('browser_execute', {
  code,
  returnScreenshot: true,
  returnDOM: true
});

// Claude can analyze the screenshot and DOM to verify success
```

## Security Best Practices

1. **Domain Allowlisting**: Only allow execution on specified domains
2. **Code Sandboxing**: Execute in isolated contexts when possible
3. **Input Validation**: Validate and sanitize all inputs
4. **Rate Limiting**: Prevent abuse through rate limits
5. **Audit Logging**: Log all executions for security review
6. **Timeout Protection**: Enforce maximum execution times
7. **Resource Limits**: Limit memory and CPU usage

## Implementation Phases

### Phase 1: Basic JS Execution
- Execute simple JavaScript
- Return execution results
- Basic error handling

### Phase 2: Rich Data Returns
- Screenshot capture
- DOM extraction
- Metadata collection

### Phase 3: Advanced Features
- Site-specific configurations
- Multi-tab support
- Session management
- Advanced wait conditions

### Phase 4: Security Hardening
- Implement all security measures
- Add audit logging
- Performance optimization

This design provides maximum flexibility while maintaining security and reliability. The key is allowing Claude to generate appropriate JavaScript for any situation while providing rich feedback through screenshots and DOM content.