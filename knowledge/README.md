# Knowledge Base

This directory contains learnings and best practices discovered during task execution.

## Categories
- **Tool Usage**: Optimal tool selection for specific tasks
- **Search Strategies**: Efficient patterns for finding information
- **Code Patterns**: Reusable code structures and approaches
- **Common Issues**: Solutions to frequently encountered problems
- **Performance Tips**: Ways to optimize task execution

## Format
- Document learnings in markdown format
- Include context and examples
- Tag entries for easy searching

## Current Knowledge
- **poster_design_learnings.md** - Best practices and insights from creating HTML event posters