#!/bin/bash

# Restart Chrome with debugging enabled
# This script safely closes existing Chrome and starts it with debugging

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 Restarting Chrome with debugging enabled...${NC}"

# Check if Chrome is running
if pgrep -f "Google Chrome" > /dev/null; then
    echo -e "${YELLOW}⚠️  Chrome is currently running${NC}"
    echo -e "${BLUE}🛑 Closing Chrome...${NC}"
    
    # Try graceful shutdown first
    osascript -e 'tell application "Google Chrome" to quit' 2>/dev/null || true
    
    # Wait a moment
    sleep 2
    
    # Force kill if still running
    if pgrep -f "Google Chrome" > /dev/null; then
        echo -e "${YELLOW}🔨 Force closing Chrome processes...${NC}"
        pkill -f "Google Chrome" || true
        sleep 2
    fi
    
    echo -e "${GREEN}✅ Chrome closed${NC}"
else
    echo -e "${GREEN}✅ Chrome is not running${NC}"
fi

# Start Chrome with debugging
echo -e "${BLUE}🚀 Starting Chrome with debugging...${NC}"
./start-chrome.sh
