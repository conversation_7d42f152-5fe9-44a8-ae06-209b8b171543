# Scripts

This directory contains utility scripts for various automation tasks.

## Chrome Automation
- **chrome_navigate.py** - Navigate Chrome debug instance to a specific URL
- **chrome_screenshot.py** - Take screenshots using Chrome DevTools Protocol
- **connect_to_chrome.py** - Connect to Chrome debug instance and control it

## Poster Viewing
- **interactive_poster_viewer.py** - Interactive poster viewer for HTML posters
- **open_poster_browser.py** - Open HTML poster files in the browser
- **view_poster.py** - Simple poster viewing utility

## Usage
Most scripts are designed to be run directly from the command line:
```bash
python3 script_name.py [arguments]
```

## Dependencies
- Python 3.x
- websocket-client (for Chrome automation scripts)
- Other dependencies as noted in individual scripts