#!/bin/bash

# Setup script for claude-as-agent project
# Initializes all submodules and provides helpful information

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                  Claude as Agent Setup                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}🚀 Welcome to the Claude as Agent project!${NC}"
echo ""

# Check if we're in the right directory
if [[ ! -f ".gitmodules" ]]; then
    echo -e "${RED}❌ Error: .gitmodules file not found${NC}"
    echo -e "${YELLOW}Please run this script from the root of the repository${NC}"
    exit 1
fi

# Check current submodule status
echo -e "${BLUE}📋 Checking submodule status...${NC}"
submodule_status=$(git submodule status)

# Count uninitialized submodules
uninitialized_count=$(echo "$submodule_status" | grep -c "^-" || true)

if [[ $uninitialized_count -gt 0 ]]; then
    echo -e "${YELLOW}⚠️  Found $uninitialized_count uninitialized submodule(s)${NC}"
    echo ""
    echo -e "${BLUE}🔧 Initializing submodules...${NC}"
    git submodule update --init --recursive
    echo -e "${GREEN}✅ Submodules initialized successfully!${NC}"
else
    echo -e "${GREEN}✅ All submodules are already initialized${NC}"
fi

echo ""
echo -e "${BLUE}📊 Current submodule status:${NC}"
git submodule status

echo ""
echo -e "${GREEN}🎉 Setup complete!${NC}"
echo ""
echo -e "${BLUE}📚 Available tools:${NC}"
echo -e "${BLUE}  • tools/mcp-backend - MCP backend server${NC}"
echo -e "${BLUE}  • tools/mcp-frontend - Browser extension for automation${NC}"
echo -e "${BLUE}  • tools/browser-automation - Playwright-based browser automation${NC}"
echo ""
echo -e "${BLUE}🔄 To update submodules in the future:${NC}"
echo -e "${BLUE}  ./tools/update_submodules.sh${NC}"
echo ""
echo -e "${BLUE}📖 For more information:${NC}"
echo -e "${BLUE}  • Check tools/README.md for overview${NC}"
echo -e "${BLUE}  • Each tool has its own README.md with specific instructions${NC}"
echo ""
echo -e "${GREEN}Happy coding! 🤖${NC}"
