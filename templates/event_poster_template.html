<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[EVENT TITLE] - Event Poster</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 10px;
        }
        
        .poster {
            width: 900px;
            max-width: 100%;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            overflow: hidden;
        }
        
        /* Header - Customize gradient colors as needed */
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .event-info {
            flex: 1;
        }
        
        .event-title {
            font-size: 32px;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 8px;
        }
        
        .event-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .host-logo {
            height: 100px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            margin-left: 20px;
        }
        
        .host-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        
        /* Date Banner - Customize background color as needed */
        .date-banner {
            background: #e74c3c;
            color: white;
            padding: 12px 30px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        /* Main Content Grid */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 280px;
            gap: 25px;
            padding: 25px 30px;
        }
        
        /* Left Content */
        .left-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        /* Speaker Section */
        .speaker-section {
            display: grid;
            grid-template-columns: 140px 1fr;
            gap: 20px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .speaker-photo {
            width: 140px;
            height: 140px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        
        .speaker-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .speaker-details h2 {
            font-size: 22px;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .speaker-role {
            font-size: 15px;
            color: #e74c3c;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .speaker-bio {
            font-size: 13px;
            line-height: 1.5;
            color: #495057;
        }
        
        /* Abstract/Content Section */
        .abstract-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .section-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 12px;
            font-weight: 700;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 18px;
            background: #e74c3c;
            margin-right: 10px;
            border-radius: 2px;
        }
        
        .abstract-text {
            font-size: 14px;
            line-height: 1.6;
            color: #495057;
            text-align: justify;
        }
        
        /* Right Column - QR/Registration Section */
        .qr-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .qr-label {
            font-size: 16px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .qr-code {
            width: 180px;
            height: 180px;
            margin: 0 auto 15px;
            background: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .meeting-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 13px;
        }
        
        .meeting-info p {
            margin: 4px 0;
            color: #6c757d;
        }
        
        .meeting-info strong {
            color: #495057;
        }
        
        /* Tags */
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }
        
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }
        
        /* Print optimization */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .poster {
                box-shadow: none;
                width: 100%;
                max-width: none;
            }
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }
            
            .host-logo {
                margin: 15px 0 0 0;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .speaker-section {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .speaker-photo {
                margin: 0 auto;
            }
            
            .qr-section {
                max-width: 300px;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="poster">
        <!-- Header -->
        <div class="header">
            <div class="event-info">
                <h1 class="event-title">[EVENT TITLE - Can use<br>line breaks for long titles]</h1>
                <div class="event-subtitle">[Event Series/Type]</div>
            </div>
            <div class="host-logo">
                <img src="[HOST-LOGO.png]" alt="[Host Organization Name]">
            </div>
        </div>
        
        <!-- Date Banner -->
        <div class="date-banner">
            [Day], [Month] [Date], [Year] • [Time] [Timezone]
        </div>
        
        <!-- Main Content Grid -->
        <div class="main-content">
            <!-- Left Content -->
            <div class="left-content">
                <!-- Speaker Section -->
                <div class="speaker-section">
                    <div class="speaker-photo">
                        <img src="[SPEAKER-PHOTO.jpg]" alt="[Speaker Name]">
                    </div>
                    <div class="speaker-details">
                        <h2>[Speaker Name]</h2>
                        <div class="speaker-role">[Speaker Title/Position]</div>
                        <div class="speaker-bio">
                            [Speaker bio - keep concise, highlight key achievements and expertise relevant to the talk]
                        </div>
                    </div>
                </div>
                
                <!-- Abstract Section -->
                <div class="abstract-section">
                    <h3 class="section-title">Abstract</h3>
                    <div class="abstract-text">
                        [First paragraph of abstract]
                        <br><br>
                        [Second paragraph if needed]
                    </div>
                    
                    <!-- Tags (optional) -->
                    <div class="tags">
                        <span class="tag">[Topic 1]</span>
                        <span class="tag">[Topic 2]</span>
                        <span class="tag">[Topic 3]</span>
                    </div>
                </div>
            </div>
            
            <!-- Right Column - QR Section -->
            <div class="qr-section">
                <div class="qr-label">[JOIN VIA ZOOM / REGISTER NOW]</div>
                <div class="qr-code">
                    <img src="[QR-CODE.png]" alt="[Registration/Join QR Code]">
                </div>
                <div class="meeting-info">
                    <p><strong>[Meeting ID / Registration Info]:</strong> [Value]</p>
                    <p><strong>[Passcode / Additional Info]:</strong> [Value]</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>