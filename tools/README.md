# Browser Automation MCP Extension

A flexible browser automation system that allows <PERSON> to control web browsers through a Chrome extension, using the Model Context Protocol (MCP). Originally designed for LinkedIn automation but now supports any website.

## Architecture Overview

```
┌─────────────┐     MCP      ┌─────────────┐     HTTP      ┌─────────────┐
│   Claude    │ ◄──────────► │ MCP Client  │ ◄──────────► │   Backend   │
│  (Desktop/  │    stdio      │   (auto-    │              │   Server    │
│    Code)    │              │  started)   │              │  (port 3636) │
└─────────────┘              └─────────────┘              └──────┬──────┘
                                                                  │
                                                            WebSocket
                                                                  │
                                                           ┌──────▼──────┐
                                                           │   Browser   │
                                                           │  Extension  │
                                                           │             │
                                                           └──────┬──────┘
                                                                  │
                                                            DOM Control
                                                                  │
                                                           ┌──────▼──────┐
                                                           │  Any Web    │
                                                           │    Page     │
                                                           └─────────────┘
```

### Components

1. **Persistent Backend Server** (`mcp-backend/src/backend-server.ts`)
   - Always running on port 3636
   - WebSocket server for browser extension communication
   - REST API endpoints for MCP client requests
   - Request tracking with unique IDs and timeouts
   - Waits for browser completion before responding

2. **MCP Client** (`mcp-backend/src/mcp-client.ts`)
   - Lightweight client that <PERSON> starts automatically
   - Communicates with backend server via HTTP
   - Implements MCP tool definitions
   - Handles connection failures gracefully

3. **Browser Extension** (`mcp-frontend/`)
   - Chrome/Edge extension with WebSocket client
   - Content scripts for generic DOM manipulation and code execution
   - Background service worker for persistent connection
   - Supports screenshots, DOM capture, and JavaScript execution
   - Reports operation success/failure back to backend

## Setup Instructions

### Prerequisites

- Node.js 18+ and npm
- Chrome or Edge browser
- Claude Desktop or Claude Code CLI

### Step 1: Install and Build Backend

```bash
cd mcp-backend
npm install
npm run build
```

### Step 2: Start the Backend Server

```bash
./start-backend.sh
```

This server must always be running. It:
- Listens on port 3636 for WebSocket connections
- Provides REST API for the MCP client
- Manages the request/response flow

To verify it's running:
```bash
curl http://localhost:3636/api/status
```

### Step 3: Load the Browser Extension

1. Open Chrome/Edge and navigate to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `mcp-frontend` directory
5. The extension icon should appear in your toolbar

### Step 4: Configure MCP Server

#### For project-specific configuration:

Create or edit `.mcp.json` in your project root:

```json
{
  "mcpServers": {
    "browser-automation": {
      "command": "node",
      "args": ["/absolute/path/to/mcp-backend/dist/index.js"],
      "env": {
        "BACKEND_URL": "http://localhost:3636"
      }
    }
  }
}
```

This is the recommended approach as it keeps the MCP configuration with your project.

#### Alternative: Global configuration

For Claude Desktop on macOS, create or edit:
`~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "browser-automation": {
      "command": "node",
      "args": ["/absolute/path/to/mcp-backend/dist/index.js"],
      "env": {
        "BACKEND_URL": "http://localhost:3636"
      }
    }
  }
}
```

For other platforms:
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`
- Linux: `~/.config/Claude/claude_desktop_config.json`

### Step 5: Configure Claude Code (if using Claude Code CLI)

Claude Code uses the project-specific `.mcp.json` file in your project root (see Step 4 above).

**Note:** Claude Code does not read from `~/.config/claude/claude_code_config.json`. You must use the `.mcp.json` file in your project directory.

### Step 6: Restart Claude

After adding the configuration:
1. Completely quit Claude Desktop or stop any Claude Code sessions
2. Start Claude again - it will automatically load the MCP configuration

## Usage

### Verify Setup

1. **Check backend status:**
   ```bash
   curl http://localhost:3636/api/status
   ```
   Should return connection counts and server status.

2. **Check extension connection:**
   - Click the extension icon in your browser toolbar
   - Should show "Connected to MCP Backend"

3. **Open LinkedIn:**
   - Navigate to https://www.linkedin.com/feed/
   - Make sure you're logged in

### Using with Claude

You can now ask Claude to:

```
"Navigate to https://example.com and capture a screenshot"
```

```
"Execute JavaScript on the current page to extract all links"
```

```
"Capture the DOM content of elements matching '.article-content'"
```

```
"Create a LinkedIn post saying 'Excited to share that I'm exploring AI automation!'" (legacy)
```

Claude will automatically:
1. Start the MCP client
2. Send the request to the backend server
3. Backend forwards to browser extension
4. Extension performs the action on LinkedIn
5. Results are returned to Claude

## How It Works

1. **Backend Server** runs continuously on port 3636
2. **Browser Extension** connects to backend via WebSocket
3. **Claude** starts the MCP client when needed
4. **MCP Client** forwards tool calls to the backend server
5. **Backend Server** sends requests to browser extension and waits for completion
6. **Browser Extension** performs the action and reports back
7. **Backend Server** returns the result to MCP client
8. **MCP Client** returns the result to Claude

This architecture ensures:
- The browser extension always has a server to connect to
- Multiple Claude sessions can use the same backend
- Requests are properly tracked and completed
- Errors are propagated back to Claude

## Available MCP Tools

### Browser Automation Tools
- `browser_execute`: Execute JavaScript code on any webpage
- `browser_capture_dom`: Capture DOM content and metadata from the current page
- `browser_capture_screenshot`: Take screenshots of pages or specific elements
- `browser_navigate`: Navigate to URLs and wait for page load

### LinkedIn-Specific Tools (Legacy)
- `create_linkedin_post`: Creates a post on LinkedIn with the provided content
- `get_post_data`: Retrieves the latest post data from the backend
- `show_linkedin_alert`: Shows a styled alert message on LinkedIn pages

## Features

- Execute arbitrary JavaScript code on any website
- Capture DOM content with CSS selectors
- Take full-page or element-specific screenshots
- Navigate to URLs with custom wait conditions
- Real-time communication between Claude and browser
- Request tracking with timeouts and error handling
- Supports all modern web applications

## Testing & Development

### Quick Test

```bash
# Test the backend API directly
./test-backend.sh
```

This will attempt to show an alert and create a post through the API.

### Development Mode

For development and debugging:

```bash
# Run backend in development mode with auto-reload
cd mcp-backend
npm run dev:backend

# Test MCP client without Claude
./test-mcp-client.sh
```

### Browser Extension Debugging

1. Open Chrome DevTools on the LinkedIn tab to see content script logs
2. Click "service worker" in `chrome://extensions/` to see background logs
3. All logs include timestamps and component tags for filtering

## Troubleshooting

### Extension shows "Disconnected"
- Ensure backend server is running: `./start-backend.sh`
- Check backend status: `curl http://localhost:3636/api/status`
- Reload the extension in Chrome

### "No browser extension connected" error
- Open LinkedIn.com in a browser tab
- Check extension popup - should show "Connected"
- Look for WebSocket errors in service worker console

### Post creation fails
- Ensure you're logged into LinkedIn
- Check content script console for specific errors
- LinkedIn's UI may have changed - check element selectors

### Claude can't find the MCP server
- For Claude Code: Ensure `.mcp.json` exists in your project root
- For Claude Desktop: Verify config file location based on your platform
- Ensure the path to `index.js` is absolute, not relative
- Restart Claude completely after config changes

### Port 3636 already in use
```bash
# Find process using port 3636
lsof -i :3636
# Kill the process or change port in backend-server.ts
```

## Logging

Both the backend and frontend include comprehensive logging:

### Backend Logs
- All logs are written to stderr to avoid interfering with MCP protocol
- Log levels: INFO, DEBUG, WARN, ERROR
- Shows MCP tool calls, WebSocket connections, and message flow
- Run with `./start.sh` to see colorized output

### Extension Logs
- Background service worker logs: Check extension service worker console
- Content script logs: Check LinkedIn tab's developer console
- All logs include timestamps and component tags ([BACKGROUND] or [CONTENT])
- Filter console by "MCP" to see only extension logs

### Log Examples
```
[2024-01-15T10:30:45.123Z] [INFO] Connected to MCP backend successfully
[2024-01-15T10:30:46.456Z] [DEBUG] Received message from backend { type: 'new_post', data: {...} }
[2024-01-15T10:30:47.789Z] [INFO] Starting LinkedIn post creation
```

## Security Considerations

- The backend server only accepts local connections
- No authentication is implemented (suitable for local development)
- WebSocket connections are not encrypted (ws:// not wss://)
- For production use, add authentication and use HTTPS/WSS

## Contributing

To contribute to this project:

1. Fork the repositories
2. Create a feature branch
3. Make your changes
4. Test thoroughly with both Claude Desktop and Claude Code
5. Submit a pull request

## License

This project is provided as-is for educational and development purposes.