# Playwright Browser Automation Tool

A powerful automation tool that connects to an **existing Chrome browser** using <PERSON><PERSON> and the Chrome DevTools Protocol (CDP). This tool allows you to automate tasks in your already-running Chrome browser without losing your authenticated sessions or having to restart the browser.

## 🚀 Key Features

- **Connect to existing Chrome browser** - No need to restart or lose your login sessions
- **Persistent browser sessions** - Keep your authenticated state across automation runs
- **Real-time browser control** - See automation happening in your actual browser
- **Flexible task system** - Create custom automation scripts for any website
- **Robust error handling** - Auto-retry logic and graceful failure recovery
- **CLI interface** - Easy command-line usage with multiple options
- **MCP Server** - Model Context Protocol server for AI assistant integration

## 📋 Prerequisites

- Node.js 18+ and npm
- Chrome or Chromium browser
- Chrome running with remote debugging enabled

## 🛠️ Installation

1. **Clone or navigate to the tool directory:**
   ```bash
   cd tools/browser-automation
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Install Playwright browsers (optional, for additional features):**
   ```bash
   npm run install-browsers
   ```

## 🔧 Setup

### Step 1: Start Chrome with Remote Debugging

Before using the tool, you need to start Chrome with remote debugging enabled:

```bash
# Basic setup
chrome --remote-debugging-port=9222

# With custom user data directory (recommended)
chrome --remote-debugging-port=9222 --user-data-dir=/path/to/profile

# On macOS
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222

# On Windows
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222
```

### Step 2: Verify Connection

Test that everything is working:

```bash
npm test
```

This will run a comprehensive test suite to verify your setup.

## 🎯 Usage

### Basic Commands

```bash
# Take a screenshot of the current tab
node src/automate.js --screenshot
# or use the npm script
npm run screenshot

# Take a screenshot with custom filename
node src/automate.js --screenshot my-screenshot.png

# Navigate to a URL
node src/automate.js --navigate "https://example.com"

# Execute JavaScript code
node src/automate.js --evaluate "document.title"

# Select a specific tab by title or URL
node src/automate.js --select-tab "GitHub" --screenshot

# Run a custom task
node src/automate.js --task submitForm --select-tab "Contact"
```

### Advanced Usage

```bash
# Combine multiple actions
node src/automate.js \
  --select-tab "Dashboard" \
  --navigate "https://app.example.com/dashboard" \
  --screenshot dashboard.png \
  --task extractData

# Enable debug logging
node src/automate.js --debug --task interactiveDemo

# Execute custom JavaScript with tab selection
node src/automate.js \
  --select-tab "LinkedIn" \
  --evaluate "document.querySelectorAll('article').length"
```

## 📝 Available Tasks

The tool comes with several pre-built tasks in the `tasks/` directory:

### 1. submitForm.js
Automates form submission with field filling and validation.

```bash
node src/automate.js --task submitForm --select-tab "Contact"
```

### 2. extractData.js
Extracts data from web pages including text, links, images, and custom elements.

```bash
node src/automate.js --task extractData --select-tab "GitHub"
```

### 3. interactiveDemo.js
Demonstrates various automation capabilities including scrolling, highlighting, and interactions.

```bash
node src/automate.js --task interactiveDemo
```

## 🔨 Creating Custom Tasks

Create your own automation tasks by adding JavaScript files to the `tasks/` directory:

```javascript
// tasks/myCustomTask.js
export default async function myCustomTask(page, options = {}) {
  console.log('[TASK] Starting my custom task...');
  
  // Your automation logic here
  await page.click('button#my-button');
  await page.fill('input#my-input', 'Hello World');
  
  console.log('[TASK] Custom task completed');
}
```

Then run it:
```bash
node src/automate.js --task myCustomTask
```

## 🤖 MCP Server Integration

This tool includes a Model Context Protocol (MCP) server that allows AI assistants like Claude to directly control your browser. The MCP server provides the same automation capabilities through a standardized protocol.

### MCP Server Setup

1. **Start Chrome with debugging:**
   ```bash
   ./start-chrome.sh
   ```

2. **Test the MCP server:**
   ```bash
   ./test-mcp.sh
   ```

3. **Configure your AI assistant:**

   For **Claude Desktop**, add to your configuration file:
   ```json
   {
     "mcpServers": {
       "browser-automation": {
         "command": "node",
         "args": ["/absolute/path/to/tools/browser-automation/src/mcp-server.js"]
       }
     }
   }
   ```

   For **Claude Code**, create `.mcp.json` in your project root:
   ```json
   {
     "mcpServers": {
       "browser-automation": {
         "command": "node",
         "args": ["/absolute/path/to/tools/browser-automation/src/mcp-server.js"]
       }
     }
   }
   ```

### Available MCP Tools

- **browser_screenshot** - Take screenshots with optional navigation
- **browser_navigate** - Navigate to URLs and wait for page load
- **browser_execute** - Execute JavaScript code on pages
- **browser_task** - Run predefined automation tasks
- **browser_get_page_info** - Get page information and metadata

### MCP Usage Examples

Once configured, you can ask your AI assistant:

```
"Take a screenshot of https://example.com"
"Navigate to GitHub and execute JavaScript to count the number of repositories"
"Run the extractData task on the current page"
"Get information about the current page including element counts"
```

## ⚙️ Configuration

Customize the tool by editing `config.js`:

```javascript
export const config = {
  chrome: {
    debuggingPort: 9222,        // Chrome debugging port
    maxRetries: 5,              // Connection retry attempts
    retryDelay: 2000,           // Delay between retries (ms)
    connectionTimeout: 10000,   // Connection timeout (ms)
  },
  automation: {
    defaultTimeout: 30000,      // Default operation timeout
    screenshotPath: './screenshots',
    screenshotQuality: 90,
  }
};
```

## 🔍 CLI Options

| Option | Description | Example |
|--------|-------------|---------|
| `-t, --select-tab <criteria>` | Select tab by title or URL substring | `--select-tab "GitHub"` |
| `-s, --screenshot [filename]` | Take a screenshot | `--screenshot my-pic.png` |
| `-n, --navigate <url>` | Navigate to URL | `--navigate "https://example.com"` |
| `-e, --evaluate <code>` | Execute JavaScript code | `--evaluate "document.title"` |
| `--task <name>` | Execute a custom task script | `--task submitForm` |
| `--debug` | Enable debug logging | `--debug` |

## 🛡️ Error Handling

The tool includes robust error handling:

- **Auto-retry logic** with exponential backoff
- **Connection recovery** if Chrome becomes unavailable
- **Graceful shutdown** on interruption signals
- **Detailed error reporting** with troubleshooting suggestions

## 🔧 Troubleshooting

### Chrome Connection Issues

**Error: "Chrome remote debugging not available"**

1. Make sure Chrome is running with debugging enabled:
   ```bash
   chrome --remote-debugging-port=9222
   ```

2. Check if the port is in use:
   ```bash
   lsof -i :9222
   ```

3. Verify the debugging endpoint:
   ```bash
   curl http://localhost:9222/json/version
   ```

### No Tabs Found

**Error: "No tabs found in Chrome browser"**

- Open some tabs in your Chrome browser
- Make sure Chrome is not running in incognito mode only
- Try refreshing existing tabs

### Task Execution Failures

- Check that the target website is loaded and accessible
- Verify element selectors in your custom tasks
- Use `--debug` flag for detailed logging

## 🔒 Security Considerations

- Remote debugging exposes full browser control - use only in trusted environments
- The tool connects to `localhost` only by default
- Consider firewall rules if running on shared machines
- Be cautious with custom JavaScript execution

## 📁 Project Structure

```
browser-automation/
├── index.js                 # Main entry point
├── package.json             # Dependencies and scripts
├── README.md                # This documentation
├── start-chrome.sh          # Chrome launcher script
├── setup.sh                 # Installation script
├── src/                     # Source code
│   ├── automate.js          # Main automation script
│   ├── mcp-server.js        # MCP server for AI assistant integration
│   ├── config.js            # Configuration settings
│   ├── tasks/               # Custom task scripts
│   │   ├── submitForm.js    # Form submission automation
│   │   ├── extractData.js   # Data extraction utilities
│   │   └── interactiveDemo.js # Interactive demonstration
│   ├── utils/               # Utility modules
│   │   └── errorHandler.js  # Error handling and retry logic
│   └── test/                # Test scripts
│       └── test-connection.js # Connection and functionality tests
├── docs/                    # Documentation
│   ├── design.md            # Original design document
│   └── examples.md          # Practical usage examples
├── test-mcp.sh              # MCP server test script
├── mcp-config-example.json  # Example MCP configuration
└── screenshots/             # Generated screenshots
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your improvements
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Happy Automating! 🤖**
