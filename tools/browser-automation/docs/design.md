---

# 📄 **Design Document: Playwright-based Automation Tool (Persistent Chrome Debugging Window)**

---

## **1. Overview**

This tool automates tasks inside an **already running Chrome browser** by attaching to its **Remote Debugging Protocol** interface.
It leverages **<PERSON>wright’s `connectOverCDP`** method to:

* Reuse authenticated sessions.
* Control active tabs or create new ones.
* Leave the browser running after automation finishes.

---

## **2. Key Goals**

* Connect to an *existing* Chrome browser with remote debugging enabled.
* Access and control already opened tabs (for automation post-login).
* Automate actions such as clicking, typing, scrolling, screenshotting.
* Leave Chrome open after automation ends (no forced browser shutdown).
* Minimize repetitive logins or re-launching browsers.

---

## **3. Architecture**

### **Components:**

| Component           | Description                                                                |
| ------------------- | -------------------------------------------------------------------------- |
| **Chrome Browser**  | Pre-opened manually or via script, with `--remote-debugging-port` enabled. |
| **Automation Tool** | Node.js script using Playwright to connect via CDP WebSocket.              |

---

### **Data Flow:**

1. **User manually launches Chrome** with:

```bash
chrome --remote-debugging-port=9222
```

2. Tool connects to CDP endpoint:

```http
http://localhost:9222/json/version
```

3. Tool connects via WebSocket using Playwright:

```js
chromium.connectOverCDP();
```

4. Tool:

   * Lists existing tabs.
   * Selects or creates tabs for automation.
   * Executes automation tasks.

5. Tool leaves Chrome open after completing tasks.

---

## **4. Key Technologies**

| Technology                           | Usage                          |
| ------------------------------------ | ------------------------------ |
| **Playwright**                       | Automation & CDP connection.   |
| **Chromium DevTools Protocol (CDP)** | Browser control.               |
| **Node.js**                          | Runtime for automation script. |

---

## **5. Detailed Workflow**

### Step 1: Launch Chrome

Manually or via script:

```bash
chrome --remote-debugging-port=9222 --user-data-dir=/path/to/profile
```

### Step 2: Discover Debugger URL

Tool fetches:

```http
http://localhost:9222/json/version
```

Extracts:

```json
{
  "webSocketDebuggerUrl": "ws://localhost:9222/devtools/browser/..."
}
```

### Step 3: Connect Playwright to Browser

```js
const browser = await chromium.connectOverCDP('http://localhost:9222');
```

### Step 4: Discover Tabs

```js
const context = browser.contexts()[0];
const pages = context.pages();
```

### Step 5: Automation Actions (Example)

```js
const page = pages[0]; // Select desired page
await page.bringToFront();
await page.screenshot({ path: 'screenshot.png' });
await page.click('button#submit');
await page.fill('input#username', 'myUsername');
```

### Step 6: Leave Browser Open

Simply do **not** call `browser.close()`:

```js
// Disconnect Playwright only
await browser.disconnect();
```

---

## **6. Error Handling**

* Auto-retry connection if Chrome isn’t ready.
* Fail gracefully if no tabs found.
* Provide CLI flag to select a specific tab by URL or title substring.

---

## **7. CLI Interface (Optional Future Feature)**

```bash
node automate.js --select-tab "Dashboard" --task "submit-form"
```

---

## **8. Security Considerations**

* Remote Debugging exposes full browser control — ensure trusted environments.
* Consider IP firewall rules for machines running automation.

---

## **9. Benefits**

* No need for headless re-logins.
* Can observe automation in real-time on the real browser.
* Full flexibility via Playwright APIs.

---

## **10. Future Enhancements**

* Interactive CLI to select tabs.
* Automation task templates.
* Remote SSH-based control of remote browsers.

---

## **11. Sample Folder Structure**

```
automation-tool/
│
├── automate.js       # Main automation script
├── config.js         # Configurations (port, task presets)
├── package.json
├── README.md
└── tasks/
    └── submitForm.js # Example task script
```

---

## **12. Risks**

| Risk                             | Mitigation                                   |
| -------------------------------- | -------------------------------------------- |
| Debugging port exposed to public | Bind to localhost, use firewall              |
| Browser crashes                  | Implement auto-recovery / reconnection logic |
| Tab selection mismatch           | Provide tab selector CLI / config            |

---

## **13. Alternatives Considered**

* Puppeteer → Similar CDP-based control but Playwright offers better multi-context support.
* Chrome Extensions → Limited compared to CDP-based automation.
* Headless Browser with Saved Sessions → Less flexible for persistent login via GUI.

---

## **14. Conclusion**

This tool offers a practical, robust way to:

* Reuse logged-in sessions in Chrome.
* Automate complex workflows without relaunching browsers.
* Support power-user automation scenarios beyond what extensions can achieve.
