# Browser Automation Examples

This document provides practical examples of using the Playwright Browser Automation Tool for various common tasks.

## 🚀 Getting Started Examples

### Basic Setup and Testing

```bash
# 1. Start Chrome with debugging
./start-chrome.sh

# 2. Test the connection
npm test

# 3. Take your first screenshot
node automate.js --screenshot first-test.png
```

## 📸 Screenshot Examples

### Basic Screenshots

```bash
# Screenshot of current active tab
node automate.js --screenshot

# Screenshot with custom filename
node automate.js --screenshot "homepage-$(date +%Y%m%d).png"

# Screenshot of specific tab
node automate.js --select-tab "GitHub" --screenshot github-page.png
```

### Advanced Screenshot Scenarios

```bash
# Screenshot after navigation
node automate.js \
  --navigate "https://example.com" \
  --screenshot example-homepage.png

# Multiple screenshots with tab switching
node automate.js --select-tab "Dashboard" --screenshot dashboard.png
node automate.js --select-tab "Profile" --screenshot profile.png
```

## 🌐 Navigation Examples

### Simple Navigation

```bash
# Navigate to a website
node automate.js --navigate "https://github.com"

# Navigate and take screenshot
node automate.js \
  --navigate "https://news.ycombinator.com" \
  --screenshot hackernews.png

# Navigate to specific tab first
node automate.js \
  --select-tab "Google" \
  --navigate "https://www.google.com/search?q=playwright"
```

### Complex Navigation Workflows

```bash
# Multi-step navigation with screenshots
node automate.js --navigate "https://github.com" --screenshot step1.png
node automate.js --navigate "https://github.com/microsoft/playwright" --screenshot step2.png
```

## 💻 JavaScript Execution Examples

### Information Extraction

```bash
# Get page title
node automate.js --evaluate "document.title"

# Get page URL and title
node automate.js --evaluate "({title: document.title, url: location.href})"

# Count elements
node automate.js --evaluate "document.querySelectorAll('a').length"

# Get all link texts
node automate.js --evaluate "Array.from(document.querySelectorAll('a')).map(a => a.textContent).filter(Boolean)"
```

### Page Interaction

```bash
# Scroll to bottom
node automate.js --evaluate "window.scrollTo(0, document.body.scrollHeight)"

# Click a button (be careful with this!)
node automate.js --evaluate "document.querySelector('button').click()"

# Fill a form field
node automate.js --evaluate "document.querySelector('input[type=\"search\"]').value = 'test query'"
```

### Advanced JavaScript Examples

```bash
# Get page performance metrics
node automate.js --evaluate "JSON.stringify(performance.timing, null, 2)"

# Extract all images with their sources
node automate.js --evaluate "
Array.from(document.querySelectorAll('img')).map(img => ({
  src: img.src,
  alt: img.alt,
  width: img.width,
  height: img.height
}))
"

# Get page structure information
node automate.js --evaluate "
({
  title: document.title,
  headings: Array.from(document.querySelectorAll('h1,h2,h3')).map(h => h.textContent),
  links: document.querySelectorAll('a').length,
  images: document.querySelectorAll('img').length,
  forms: document.querySelectorAll('form').length
})
"
```

## 🎯 Task-Based Examples

### Form Submission

```bash
# Basic form submission
node automate.js --task submitForm --select-tab "Contact"

# Form submission with debug logging
node automate.js --debug --task submitForm --select-tab "Contact Form"
```

### Data Extraction

```bash
# Extract data from current page
node automate.js --task extractData

# Extract data from specific tab
node automate.js --select-tab "Product Page" --task extractData

# Extract data and save to file
node automate.js --task extractData --select-tab "News"
```

### Interactive Demonstrations

```bash
# Run interactive demo on current tab
node automate.js --task interactiveDemo

# Run demo on specific website
node automate.js \
  --navigate "https://example.com" \
  --task interactiveDemo \
  --screenshot demo-result.png
```

## 🔧 Advanced Workflow Examples

### Multi-Step Automation

```bash
# Complete workflow: navigate, interact, extract, screenshot
node automate.js \
  --select-tab "Dashboard" \
  --navigate "https://app.example.com/dashboard" \
  --task extractData \
  --screenshot final-state.png
```

### Debugging and Development

```bash
# Debug mode with detailed logging
node automate.js --debug --task interactiveDemo

# Test connection and capabilities
npm test

# Quick screenshot for debugging
node automate.js --screenshot debug-$(date +%H%M%S).png
```

### Batch Operations

```bash
#!/bin/bash
# Script to automate multiple tabs

# Screenshot all important tabs
node automate.js --select-tab "Gmail" --screenshot gmail.png
node automate.js --select-tab "Calendar" --screenshot calendar.png
node automate.js --select-tab "Drive" --screenshot drive.png

# Extract data from multiple sources
node automate.js --select-tab "Analytics" --task extractData
node automate.js --select-tab "Reports" --task extractData
```

## 🌟 Real-World Use Cases

### 1. Daily Dashboard Monitoring

```bash
#!/bin/bash
# daily-monitoring.sh

echo "📊 Starting daily dashboard monitoring..."

# Take screenshots of key dashboards
node automate.js --select-tab "Analytics" --screenshot "analytics-$(date +%Y%m%d).png"
node automate.js --select-tab "Sales Dashboard" --screenshot "sales-$(date +%Y%m%d).png"

# Extract key metrics
node automate.js --select-tab "KPI Dashboard" --task extractData

echo "✅ Daily monitoring complete!"
```

### 2. Website Testing

```bash
#!/bin/bash
# website-testing.sh

SITE_URL="https://your-website.com"

echo "🧪 Testing website: $SITE_URL"

# Test homepage
node automate.js --navigate "$SITE_URL" --screenshot homepage-test.png

# Test key pages
node automate.js --navigate "$SITE_URL/about" --screenshot about-test.png
node automate.js --navigate "$SITE_URL/contact" --screenshot contact-test.png

# Run interactive tests
node automate.js --task interactiveDemo --screenshot interaction-test.png

echo "✅ Website testing complete!"
```

### 3. Data Collection

```bash
#!/bin/bash
# data-collection.sh

echo "📊 Starting data collection..."

# Collect data from multiple sources
node automate.js --select-tab "Source 1" --task extractData
node automate.js --select-tab "Source 2" --task extractData
node automate.js --select-tab "Source 3" --task extractData

# Take evidence screenshots
node automate.js --select-tab "Source 1" --screenshot source1-evidence.png
node automate.js --select-tab "Source 2" --screenshot source2-evidence.png

echo "✅ Data collection complete!"
```

## 🛠️ Custom Task Examples

### Creating a Simple Custom Task

```javascript
// tasks/checkStatus.js
export default async function checkStatus(page, options = {}) {
  console.log('[TASK] Checking page status...');
  
  const status = await page.evaluate(() => {
    return {
      title: document.title,
      loaded: document.readyState === 'complete',
      elementCount: document.querySelectorAll('*').length,
      hasErrors: !!document.querySelector('.error, .alert-danger')
    };
  });
  
  console.log('[TASK] Status:', JSON.stringify(status, null, 2));
  
  if (status.hasErrors) {
    console.log('[TASK] ⚠️ Errors detected on page');
  } else {
    console.log('[TASK] ✅ Page looks good');
  }
  
  return status;
}
```

Usage:
```bash
node automate.js --task checkStatus --select-tab "Dashboard"
```

### Advanced Custom Task with Options

```javascript
// tasks/monitorChanges.js
export default async function monitorChanges(page, options = {}) {
  const { selector = 'body', interval = 5000, duration = 30000 } = options;
  
  console.log(`[TASK] Monitoring changes in ${selector} for ${duration}ms...`);
  
  let previousContent = await page.locator(selector).textContent();
  const startTime = Date.now();
  
  while (Date.now() - startTime < duration) {
    await page.waitForTimeout(interval);
    
    const currentContent = await page.locator(selector).textContent();
    
    if (currentContent !== previousContent) {
      console.log('[TASK] 🔄 Content changed detected!');
      await page.screenshot({ 
        path: `./screenshots/change-detected-${Date.now()}.png` 
      });
      previousContent = currentContent;
    }
  }
  
  console.log('[TASK] ✅ Monitoring complete');
}
```

## 💡 Tips and Best Practices

### 1. Tab Selection Strategies

```bash
# Select by exact title match
node automate.js --select-tab "GitHub"

# Select by URL pattern
node automate.js --select-tab "github.com"

# Select by partial title
node automate.js --select-tab "Dashboard"
```

### 2. Error Handling

```bash
# Use debug mode for troubleshooting
node automate.js --debug --task myTask

# Test connection first
npm test

# Take screenshot before complex operations
node automate.js --screenshot before-operation.png
```

### 3. Performance Optimization

```bash
# Use specific tab selection to avoid unnecessary operations
node automate.js --select-tab "specific-page" --task quickTask

# Combine operations in single command
node automate.js --navigate "https://example.com" --task extractData --screenshot result.png
```

---

These examples should give you a solid foundation for automating your browser tasks. Remember to always test your automation scripts in a safe environment first!
