#!/bin/bash

# Chrome Profile Management Script
# Helps manage Chrome profiles for browser automation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Default profile directory
DEFAULT_PROFILE_DIR="$HOME/.chrome-automation"
PROFILE_DIR="${CHROME_PROFILE_DIR:-$DEFAULT_PROFILE_DIR}"

echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                Chrome Profile Management                     ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Function to show profile info
show_profile_info() {
    echo -e "${BLUE}📁 Profile Information:${NC}"
    echo -e "${BLUE}  Location: $PROFILE_DIR${NC}"
    
    if [[ -d "$PROFILE_DIR" ]]; then
        echo -e "${GREEN}  Status: Exists${NC}"
        
        # Get profile size
        if command -v du >/dev/null 2>&1; then
            PROFILE_SIZE=$(du -sh "$PROFILE_DIR" 2>/dev/null | cut -f1 || echo "Unknown")
            echo -e "${BLUE}  Size: $PROFILE_SIZE${NC}"
        fi
        
        # Check for important files
        if [[ -f "$PROFILE_DIR/Default/Preferences" ]]; then
            echo -e "${GREEN}  ✅ Preferences found${NC}"
        fi
        
        if [[ -f "$PROFILE_DIR/Default/Login Data" ]]; then
            echo -e "${GREEN}  ✅ Login data found${NC}"
        fi
        
        if [[ -f "$PROFILE_DIR/Default/Bookmarks" ]]; then
            echo -e "${GREEN}  ✅ Bookmarks found${NC}"
        fi
        
        if [[ -d "$PROFILE_DIR/Default/Extensions" ]]; then
            EXT_COUNT=$(find "$PROFILE_DIR/Default/Extensions" -mindepth 1 -maxdepth 1 -type d 2>/dev/null | wc -l || echo "0")
            echo -e "${GREEN}  ✅ Extensions: $EXT_COUNT installed${NC}"
        fi
        
    else
        echo -e "${YELLOW}  Status: Does not exist${NC}"
        echo -e "${BLUE}  ℹ️  Will be created when Chrome starts${NC}"
    fi
    echo ""
}

# Function to backup profile
backup_profile() {
    if [[ ! -d "$PROFILE_DIR" ]]; then
        echo -e "${RED}❌ Profile directory does not exist: $PROFILE_DIR${NC}"
        return 1
    fi
    
    BACKUP_NAME="chrome-automation-backup-$(date +%Y%m%d-%H%M%S)"
    BACKUP_PATH="$HOME/$BACKUP_NAME"
    
    echo -e "${BLUE}💾 Creating profile backup...${NC}"
    echo -e "${BLUE}  From: $PROFILE_DIR${NC}"
    echo -e "${BLUE}  To: $BACKUP_PATH${NC}"
    
    cp -r "$PROFILE_DIR" "$BACKUP_PATH"
    
    echo -e "${GREEN}✅ Backup created successfully!${NC}"
    echo -e "${BLUE}  Backup location: $BACKUP_PATH${NC}"
    echo ""
}

# Function to restore profile
restore_profile() {
    echo -e "${BLUE}📂 Available backups in $HOME:${NC}"
    
    BACKUPS=($(find "$HOME" -maxdepth 1 -name "chrome-automation-backup-*" -type d 2>/dev/null | sort -r))
    
    if [[ ${#BACKUPS[@]} -eq 0 ]]; then
        echo -e "${YELLOW}  No backups found${NC}"
        return 1
    fi
    
    for i in "${!BACKUPS[@]}"; do
        BACKUP_NAME=$(basename "${BACKUPS[$i]}")
        echo -e "${BLUE}  [$((i+1))] $BACKUP_NAME${NC}"
    done
    
    echo ""
    read -p "Enter backup number to restore (or 'q' to quit): " choice
    
    if [[ "$choice" == "q" ]]; then
        return 0
    fi
    
    if [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -ge 1 ]] && [[ "$choice" -le ${#BACKUPS[@]} ]]; then
        SELECTED_BACKUP="${BACKUPS[$((choice-1))]}"
        
        echo -e "${YELLOW}⚠️  This will replace the current profile. Continue? (y/N)${NC}"
        read -p "> " confirm
        
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            if [[ -d "$PROFILE_DIR" ]]; then
                echo -e "${BLUE}🗑️  Removing current profile...${NC}"
                rm -rf "$PROFILE_DIR"
            fi
            
            echo -e "${BLUE}📥 Restoring backup...${NC}"
            cp -r "$SELECTED_BACKUP" "$PROFILE_DIR"
            
            echo -e "${GREEN}✅ Profile restored successfully!${NC}"
        else
            echo -e "${BLUE}ℹ️  Restore cancelled${NC}"
        fi
    else
        echo -e "${RED}❌ Invalid selection${NC}"
    fi
    echo ""
}

# Function to reset profile
reset_profile() {
    if [[ ! -d "$PROFILE_DIR" ]]; then
        echo -e "${YELLOW}ℹ️  Profile directory does not exist, nothing to reset${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  This will permanently delete the current profile and all saved data.${NC}"
    echo -e "${YELLOW}    This includes: logins, bookmarks, extensions, browsing history${NC}"
    echo ""
    echo -e "${RED}Are you sure you want to reset the profile? (y/N)${NC}"
    read -p "> " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🗑️  Removing profile directory...${NC}"
        rm -rf "$PROFILE_DIR"
        echo -e "${GREEN}✅ Profile reset successfully!${NC}"
        echo -e "${BLUE}ℹ️  A new profile will be created when Chrome starts${NC}"
    else
        echo -e "${BLUE}ℹ️  Reset cancelled${NC}"
    fi
    echo ""
}

# Function to show usage
show_usage() {
    echo -e "${BLUE}Usage: $0 [command]${NC}"
    echo ""
    echo -e "${BLUE}Commands:${NC}"
    echo -e "${BLUE}  info     Show profile information (default)${NC}"
    echo -e "${BLUE}  backup   Create a backup of the current profile${NC}"
    echo -e "${BLUE}  restore  Restore from a backup${NC}"
    echo -e "${BLUE}  reset    Delete the current profile (fresh start)${NC}"
    echo -e "${BLUE}  help     Show this help message${NC}"
    echo ""
    echo -e "${BLUE}Environment Variables:${NC}"
    echo -e "${BLUE}  CHROME_PROFILE_DIR   Custom profile directory${NC}"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo -e "${BLUE}  $0                                    # Show profile info${NC}"
    echo -e "${BLUE}  $0 backup                             # Backup current profile${NC}"
    echo -e "${BLUE}  CHROME_PROFILE_DIR=/tmp/test $0 info  # Use custom profile${NC}"
    echo ""
}

# Main script logic
case "${1:-info}" in
    "info")
        show_profile_info
        ;;
    "backup")
        show_profile_info
        backup_profile
        ;;
    "restore")
        show_profile_info
        restore_profile
        ;;
    "reset")
        show_profile_info
        reset_profile
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_usage
        exit 1
        ;;
esac

echo -e "${GREEN}Profile management complete!${NC}"
