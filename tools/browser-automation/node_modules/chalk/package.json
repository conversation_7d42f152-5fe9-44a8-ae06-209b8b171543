{"name": "chalk", "version": "5.4.1", "description": "Terminal string styling done right", "license": "MIT", "repository": "chalk/chalk", "funding": "https://github.com/chalk/chalk?sponsor=1", "type": "module", "main": "./source/index.js", "exports": "./source/index.js", "imports": {"#ansi-styles": "./source/vendor/ansi-styles/index.js", "#supports-color": {"node": "./source/vendor/supports-color/index.js", "default": "./source/vendor/supports-color/browser.js"}}, "types": "./source/index.d.ts", "sideEffects": false, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "scripts": {"test": "xo && c8 ava && tsd", "bench": "matcha benchmark.js"}, "files": ["source", "!source/index.test-d.ts"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "string", "ansi", "style", "styles", "tty", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.57.0", "yoctodelay": "^2.0.0"}, "xo": {"rules": {"unicorn/prefer-string-slice": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-exports": "off", "@typescript-eslint/consistent-type-definitions": "off", "unicorn/expiring-todo-comments": "off"}}, "c8": {"reporter": ["text", "lcov"], "exclude": ["source/vendor"]}}