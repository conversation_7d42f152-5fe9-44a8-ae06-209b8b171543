{"name": "playwright-browser-automation", "version": "1.0.0", "description": "Playwright-based automation tool that connects to existing Chrome browser via CDP", "main": "automate.js", "type": "module", "scripts": {"start": "node automate.js", "install-browsers": "npx playwright install chromium", "test": "node test/test-connection.js", "start-chrome": "./start-chrome.sh", "screenshot": "node automate.js --screenshot", "demo": "node automate.js --task interactiveDemo", "help": "node automate.js --help"}, "keywords": ["playwright", "automation", "chrome", "cdp", "browser-automation"], "author": "Browser Automation Tool", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "commander": "^11.1.0", "chalk": "^5.3.0"}, "devDependencies": {"@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0"}}