{"name": "playwright-browser-automation", "version": "1.0.0", "description": "Playwright-based automation tool that connects to existing Chrome browser via CDP", "main": "index.js", "type": "module", "bin": {"automate": "./index.js"}, "scripts": {"start": "node src/automate.js", "install-browsers": "npx playwright install chromium", "test": "node src/test/test-connection.js", "start-chrome": "./start-chrome.sh", "screenshot": "node src/automate.js --screenshot", "demo": "node src/automate.js --task interactiveDemo", "help": "node src/automate.js --help", "mcp": "node src/mcp-server.js", "mcp-client": "node src/mcp-client.js", "remote-server": "node src/remote-server.js"}, "keywords": ["playwright", "automation", "chrome", "cdp", "browser-automation"], "author": "Browser Automation Tool", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "commander": "^11.1.0", "chalk": "^5.3.0", "@modelcontextprotocol/sdk": "^1.13.2", "express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0"}}