#!/bin/bash

# Setup script for Playwright Browser Automation Tool
# This script installs dependencies and verifies the setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║           Playwright Browser Automation Tool Setup          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Check Node.js version
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    echo -e "${YELLOW}Please install Node.js 18+ from https://nodejs.org${NC}"
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo -e "${RED}❌ Node.js version $NODE_VERSION is too old${NC}"
    echo -e "${YELLOW}Please upgrade to Node.js 18 or higher${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js $(node --version) is installed${NC}"

# Check npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ npm $(npm --version) is installed${NC}"

# Install dependencies
echo -e "${BLUE}📦 Installing dependencies...${NC}"

if [ -f "package.json" ]; then
    npm install
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
    echo -e "${RED}❌ package.json not found${NC}"
    echo -e "${YELLOW}Make sure you're in the browser-automation directory${NC}"
    exit 1
fi

# Create directories
echo -e "${BLUE}📁 Creating directories...${NC}"
mkdir -p screenshots
mkdir -p tasks
mkdir -p test
mkdir -p utils
echo -e "${GREEN}✅ Directories created${NC}"

# Make scripts executable
echo -e "${BLUE}🔧 Setting up scripts...${NC}"
chmod +x start-chrome.sh
chmod +x setup.sh
echo -e "${GREEN}✅ Scripts are now executable${NC}"

# Check for Chrome
echo -e "${BLUE}🌐 Checking for Chrome installation...${NC}"

CHROME_FOUND=false

if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if [[ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]]; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Google Chrome found on macOS${NC}"
    elif [[ -f "/Applications/Chromium.app/Contents/MacOS/Chromium" ]]; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Chromium found on macOS${NC}"
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v google-chrome &> /dev/null; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Google Chrome found on Linux${NC}"
    elif command -v chromium-browser &> /dev/null; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Chromium found on Linux${NC}"
    elif command -v chromium &> /dev/null; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Chromium found on Linux${NC}"
    fi
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows
    if [[ -f "C:/Program Files/Google/Chrome/Application/chrome.exe" ]] || [[ -f "C:/Program Files (x86)/Google/Chrome/Application/chrome.exe" ]]; then
        CHROME_FOUND=true
        echo -e "${GREEN}✅ Google Chrome found on Windows${NC}"
    fi
fi

if [ "$CHROME_FOUND" = false ]; then
    echo -e "${YELLOW}⚠️  Chrome/Chromium not found in standard locations${NC}"
    echo -e "${YELLOW}Please install Chrome or Chromium to use this tool${NC}"
fi

# Test the setup
echo -e "${BLUE}🧪 Testing the setup...${NC}"

# Check if we can run the help command
if node automate.js --help > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Main script is working${NC}"
else
    echo -e "${RED}❌ Main script has issues${NC}"
    exit 1
fi

# Final setup complete message
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🎉 Setup Complete! 🎉                    ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}📋 Next Steps:${NC}"
echo ""
echo -e "${YELLOW}1. Start Chrome with debugging:${NC}"
echo "   ./start-chrome.sh"
echo ""
echo -e "${YELLOW}2. Test the connection:${NC}"
echo "   npm test"
echo ""
echo -e "${YELLOW}3. Take your first screenshot:${NC}"
echo "   npm run screenshot"
echo ""
echo -e "${YELLOW}4. Try the interactive demo:${NC}"
echo "   npm run demo"
echo ""
echo -e "${YELLOW}5. Get help:${NC}"
echo "   node automate.js --help"
echo ""

echo -e "${BLUE}📚 Documentation:${NC}"
echo "   • README.md - Complete documentation"
echo "   • examples.md - Practical examples"
echo "   • tasks/ - Custom task scripts"
echo ""

echo -e "${BLUE}🔗 Quick Commands:${NC}"
echo "   npm run start-chrome  # Start Chrome with debugging"
echo "   npm test              # Test connection"
echo "   npm run screenshot    # Take a screenshot"
echo "   npm run demo          # Interactive demonstration"
echo ""

echo -e "${GREEN}Happy automating! 🤖${NC}"
