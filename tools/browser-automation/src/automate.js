#!/usr/bin/env node

/**
 * Playwright-based Browser Automation Tool
 * Connects to existing Chrome browser via Chrome DevTools Protocol (CDP)
 */

import { chromium } from 'playwright';
import { Command } from 'commander';
import chalk from 'chalk';
import fs from 'fs/promises';
import path from 'path';
import { config, getChromeEndpoint } from './config.js';
import {
  retryWithBackoff,
  withTimeout,
  safeElementInteraction,
  safeNavigate,
  AutomationError,
  ConnectionError,
  TaskError,
  ErrorTypes,
  reportError,
  setupGracefulShutdown
} from './utils/errorHandler.js';

const program = new Command();

// Logger utility
const log = {
  info: (msg, ...args) => console.log(chalk.blue('[INFO]'), msg, ...args),
  warn: (msg, ...args) => console.log(chalk.yellow('[WARN]'), msg, ...args),
  error: (msg, ...args) => console.log(chalk.red('[ERROR]'), msg, ...args),
  success: (msg, ...args) => console.log(chalk.green('[SUCCESS]'), msg, ...args),
  debug: (msg, ...args) => config.logging.level === 'debug' && console.log(chalk.gray('[DEBUG]'), msg, ...args)
};

/**
 * Check if Chrome is running with remote debugging enabled
 */
async function checkChromeConnection() {
  try {
    const response = await fetch(`${getChromeEndpoint()}/json/version`);
    if (response.ok) {
      const data = await response.json();
      log.debug('Chrome version info:', data);
      return true;
    }
    return false;
  } catch (error) {
    log.debug('Chrome connection check failed:', error.message);
    return false;
  }
}

/**
 * Connect to Chrome browser via CDP
 */
async function connectToChrome() {
  return retryWithBackoff(async () => {
    log.info('Attempting to connect to Chrome...');

    // Check if Chrome is available
    const isAvailable = await checkChromeConnection();
    if (!isAvailable) {
      throw new ConnectionError(
        'Chrome remote debugging not available',
        { endpoint: getChromeEndpoint() }
      );
    }

    // Connect to Chrome with timeout
    const browser = await withTimeout(
      chromium.connectOverCDP(getChromeEndpoint()),
      config.chrome.connectionTimeout,
      'Chrome connection timed out'
    );

    log.success('Successfully connected to Chrome browser');
    return browser;

  }, config.chrome.maxRetries, config.chrome.retryDelay);
}

/**
 * List all available tabs/pages
 */
async function listTabs(browser) {
  const contexts = browser.contexts();
  const allPages = [];
  
  for (const context of contexts) {
    const pages = context.pages();
    for (const page of pages) {
      try {
        const title = await page.title();
        const url = page.url();
        allPages.push({ page, title, url, context });
      } catch (error) {
        log.debug('Error getting page info:', error.message);
      }
    }
  }
  
  return allPages;
}

/**
 * Select a tab based on criteria
 */
function selectTab(tabs, criteria) {
  if (!criteria) {
    return tabs[0]; // Return first tab if no criteria
  }
  
  // Try to match by title or URL
  const match = tabs.find(tab => 
    tab.title.toLowerCase().includes(criteria.toLowerCase()) ||
    tab.url.toLowerCase().includes(criteria.toLowerCase())
  );
  
  return match || tabs[0];
}

/**
 * Take a screenshot of the current page
 */
async function takeScreenshot(page, options = {}) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  let filename;

  if (typeof options.filename === 'string') {
    filename = options.filename;
  } else if (options.filename === true) {
    filename = `screenshot-${timestamp}.png`;
  } else {
    filename = `screenshot-${timestamp}.png`;
  }

  const filepath = path.join(config.automation.screenshotPath, filename);
  
  // Ensure screenshots directory exists
  await fs.mkdir(config.automation.screenshotPath, { recursive: true });
  
  const screenshotOptions = {
    path: filepath,
    fullPage: options.fullPage !== false
  };

  // Only add quality for JPEG files
  if (filepath.toLowerCase().endsWith('.jpg') || filepath.toLowerCase().endsWith('.jpeg')) {
    screenshotOptions.quality = options.quality || config.automation.screenshotQuality;
  }

  await page.screenshot(screenshotOptions);
  
  log.success(`Screenshot saved: ${filepath}`);
  return filepath;
}

/**
 * Execute a custom task script
 */
async function executeTask(page, taskName, options = {}) {
  // Use import.meta.url to get the current file's directory
  const currentDir = path.dirname(new URL(import.meta.url).pathname);
  const taskPath = path.resolve(currentDir, 'tasks', `${taskName}.js`);

  try {
    const taskModule = await import(`file://${taskPath}`);
    if (typeof taskModule.default === 'function') {
      log.info(`Executing task: ${taskName}`);
      await taskModule.default(page, options);
      log.success(`Task completed: ${taskName}`);
    } else {
      throw new Error('Task module must export a default function');
    }
  } catch (error) {
    log.error(`Failed to execute task ${taskName}:`, error.message);
    throw error;
  }
}

/**
 * Main automation function
 */
async function runAutomation(options) {
  let browser = null;

  try {
    // Connect to Chrome
    browser = await connectToChrome();

    // Set up graceful shutdown
    setupGracefulShutdown(browser);

    // List available tabs
    const tabs = await listTabs(browser);
    log.info(`Found ${tabs.length} open tabs`);

    if (tabs.length === 0) {
      throw new AutomationError(
        'No tabs found in Chrome browser',
        ErrorTypes.TAB_NOT_FOUND,
        { suggestion: 'Open some tabs in Chrome and try again' }
      );
    }

    // Display tabs
    tabs.forEach((tab, index) => {
      log.info(`  [${index}] ${tab.title} - ${tab.url}`);
    });

    // Select tab
    const selectedTab = selectTab(tabs, options.selectTab);
    log.info(`Selected tab: ${selectedTab.title}`);

    // Bring tab to front
    await selectedTab.page.bringToFront();

    // Execute requested actions with error handling
    if (options.screenshot) {
      await takeScreenshot(selectedTab.page, { filename: options.screenshot });
    }

    if (options.task) {
      await executeTask(selectedTab.page, options.task, options);
    }

    if (options.navigate) {
      log.info(`Navigating to: ${options.navigate}`);
      await safeNavigate(selectedTab.page, options.navigate);
      log.success('Navigation completed');
    }

    if (options.evaluate) {
      log.info('Executing JavaScript code...');
      const result = await withTimeout(
        selectedTab.page.evaluate(options.evaluate),
        config.automation.defaultTimeout,
        'JavaScript execution timed out'
      );
      log.success('JavaScript execution result:', result);
    }

  } catch (error) {
    reportError(error, { browser, options });
    process.exit(1);
  } finally {
    // Disconnect from browser (but leave it running)
    if (browser) {
      try {
        await browser.close();
        log.info('Disconnected from Chrome (browser remains open)');
      } catch (disconnectError) {
        log.warn('Error during disconnect:', disconnectError.message);
      }
    }
  }
}

// CLI setup
program
  .name('automate')
  .description('Playwright-based browser automation tool that connects to existing Chrome browser')
  .version('1.0.0')
  .addHelpText('before', `
🤖 Playwright Browser Automation Tool

This tool connects to an existing Chrome browser via Chrome DevTools Protocol (CDP).
Make sure Chrome is running with: chrome --remote-debugging-port=9222

Examples:
  automate --screenshot                    Take a screenshot
  automate --select-tab "GitHub" --screenshot github.png
  automate --navigate "https://example.com" --screenshot
  automate --task interactiveDemo         Run interactive demo
  automate --evaluate "document.title"    Execute JavaScript
`)
  .addHelpText('after', `
📚 More Information:
  • README.md - Complete documentation
  • examples.md - Practical usage examples
  • npm test - Test your setup
  • npm run start-chrome - Start Chrome with debugging

🔗 Available Tasks:
  • submitForm - Automate form submission
  • extractData - Extract data from pages
  • interactiveDemo - Interactive demonstration
`);

program
  .option('-t, --select-tab <criteria>', 'Select tab by title or URL substring')
  .option('-s, --screenshot [filename]', 'Take a screenshot (optional custom filename)')
  .option('-n, --navigate <url>', 'Navigate to specified URL')
  .option('-e, --evaluate <code>', 'Execute JavaScript code on the page')
  .option('--task <name>', 'Execute a custom task script from tasks/ directory')
  .option('--debug', 'Enable debug logging for troubleshooting')
  .action(async (options) => {
    if (options.debug) {
      config.logging.level = 'debug';
    }

    // Show helpful message if no options provided
    if (Object.keys(options).length === 0) {
      log.info('No actions specified. Use --help to see available options.');
      log.info('Quick start: automate --screenshot');
      return;
    }

    await runAutomation(options);
  });

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run CLI
if (import.meta.url === `file://${process.argv[1]}`) {
  program.parse();
}

export { runAutomation, connectToChrome, listTabs, takeScreenshot, executeTask };
