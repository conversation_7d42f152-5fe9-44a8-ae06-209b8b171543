/**
 * Configuration module for Playwright Browser Automation Tool
 */

export const config = {
  // Chrome Remote Debugging Configuration
  chrome: {
    debuggingPort: 9222,
    host: 'localhost',
    maxRetries: 5,
    retryDelay: 2000, // milliseconds
    connectionTimeout: 10000, // milliseconds
  },

  // Default automation settings
  automation: {
    defaultTimeout: 30000, // milliseconds
    screenshotPath: './screenshots',
    screenshotQuality: 90,
    waitForSelector: 5000, // milliseconds
  },

  // Task presets
  taskPresets: {
    'screenshot': {
      description: 'Take a screenshot of the current page',
      actions: ['screenshot']
    },
    'submit-form': {
      description: 'Submit a form on the current page',
      actions: ['fill-form', 'submit']
    },
    'navigate': {
      description: 'Navigate to a specific URL',
      actions: ['goto']
    },
    'extract-data': {
      description: 'Extract data from page elements',
      actions: ['extract']
    }
  },

  // Logging configuration
  logging: {
    level: 'info', // 'debug', 'info', 'warn', 'error'
    timestamp: true,
    colors: true
  }
};

/**
 * Get Chrome debugging endpoint URL
 */
export function getChromeEndpoint() {
  return `http://${config.chrome.host}:${config.chrome.debuggingPort}`;
}

/**
 * Get Chrome WebSocket debugging URL
 */
export function getChromeWebSocketUrl() {
  return `ws://${config.chrome.host}:${config.chrome.debuggingPort}`;
}

/**
 * Override default configuration with user-provided options
 */
export function updateConfig(userConfig) {
  return Object.assign({}, config, userConfig);
}

export default config;
