#!/usr/bin/env node

/**
 * MCP Client for Browser Automation
 * Connects to the remote server on port 3636 and provides MCP tools
 * Similar to the mcp-backend/mcp-client architecture
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// Remote server URL
const REMOTE_SERVER_URL = process.env.REMOTE_SERVER_URL || 'http://localhost:3636';

// Logger utility (to stderr to avoid interfering with MCP protocol)
const log = (level, message, data) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [MCP-CLIENT] [${level}] ${message}`;
  if (data) {
    console.error(logMessage, JSON.stringify(data, null, 2));
  } else {
    console.error(logMessage);
  }
};

// Create MCP server
const server = new Server(
  {
    name: 'browser-automation-mcp-client',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

log('INFO', 'Browser Automation MCP Client initialized', { remoteServerUrl: REMOTE_SERVER_URL });

// Check remote server connectivity
async function checkRemoteServer() {
  try {
    const response = await fetch(`${REMOTE_SERVER_URL}/api/status`);
    if (response.ok) {
      const status = await response.json();
      log('INFO', 'Remote server status', status);
      return true;
    }
    return false;
  } catch (error) {
    log('ERROR', 'Failed to connect to remote server', { 
      error: error instanceof Error ? error.message : String(error),
      url: REMOTE_SERVER_URL
    });
    return false;
  }
}

// Make API request to remote server
async function makeApiRequest(endpoint, method = 'GET', data = null) {
  const url = `${REMOTE_SERVER_URL}/api/${endpoint}`;
  
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }
  
  log('DEBUG', `Making ${method} request to ${endpoint}`, data);
  
  const response = await fetch(url, options);
  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`);
  }
  
  return result;
}

// Define tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  log('DEBUG', 'Received ListTools request');
  
  // Check if remote server is available
  const isAvailable = await checkRemoteServer();
  if (!isAvailable) {
    log('WARN', 'Remote server not available, tools may not work');
  }
  
  return {
    tools: [
      {
        name: 'browser_screenshot',
        description: 'Take a screenshot of the current page or navigate to a URL and take a screenshot',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL to navigate to before taking screenshot (optional)',
            },
            filename: {
              type: 'string',
              description: 'Custom filename for the screenshot (optional)',
            },
            fullPage: {
              type: 'boolean',
              description: 'Capture full scrollable page (not just visible viewport)',
              default: true,
            },
            quality: {
              type: 'number',
              description: 'Image quality for JPEG files (1-100)',
              default: 90,
            },
          },
        },
      },
      {
        name: 'browser_navigate',
        description: 'Navigate to a URL and wait for it to load',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL to navigate to',
            },
            waitUntil: {
              type: 'string',
              description: 'When to consider navigation complete',
              enum: ['load', 'domcontentloaded', 'networkidle'],
              default: 'networkidle',
            },
          },
          required: ['url'],
        },
      },
      {
        name: 'browser_execute',
        description: 'Execute JavaScript code on the current page and return the result',
        inputSchema: {
          type: 'object',
          properties: {
            code: {
              type: 'string',
              description: 'JavaScript code to execute',
            },
            url: {
              type: 'string',
              description: 'URL to navigate to before executing code (optional)',
            },
          },
          required: ['code'],
        },
      },
      {
        name: 'browser_task',
        description: 'Execute a predefined automation task (submitForm, extractData, interactiveDemo)',
        inputSchema: {
          type: 'object',
          properties: {
            taskName: {
              type: 'string',
              description: 'Name of the task to execute',
              enum: ['submitForm', 'extractData', 'interactiveDemo'],
            },
            url: {
              type: 'string',
              description: 'URL to navigate to before executing task (optional)',
            },
            options: {
              type: 'object',
              description: 'Task-specific options (optional)',
            },
          },
          required: ['taskName'],
        },
      },
      {
        name: 'browser_get_page_info',
        description: 'Get information about the current page (title, URL, element counts, etc.)',
        inputSchema: {
          type: 'object',
          properties: {
            includeContent: {
              type: 'boolean',
              description: 'Include page content in the response',
              default: false,
            },
          },
        },
      },
    ],
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  log('INFO', `Received tool call: ${name}`, args);

  try {
    let result;

    switch (name) {
      case 'browser_screenshot':
        result = await makeApiRequest('screenshot', 'POST', args);
        break;

      case 'browser_navigate':
        result = await makeApiRequest('navigate', 'POST', args);
        break;

      case 'browser_execute':
        result = await makeApiRequest('execute', 'POST', args);
        break;

      case 'browser_task':
        result = await makeApiRequest('task', 'POST', {
          taskName: args.taskName,
          url: args.url,
          options: args.options || {}
        });
        break;

      case 'browser_get_page_info':
        const endpoint = args.includeContent ? 'page-info?includeContent=true' : 'page-info';
        result = await makeApiRequest(endpoint, 'GET');
        break;

      default:
        throw new Error(`Unknown tool: ${name}`);
    }

    log('DEBUG', `Tool ${name} completed successfully`);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };

  } catch (error) {
    log('ERROR', `Tool execution failed: ${name}`, error);
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            success: false,
            error: error.message,
            tool: name,
            remoteServerUrl: REMOTE_SERVER_URL,
            suggestion: 'Make sure the remote server is running on port 3636'
          }, null, 2),
        },
      ],
      isError: true,
    };
  }
});

// Start the MCP client
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  log('INFO', 'Browser Automation MCP Client started');
  
  // Initial connectivity check
  const isConnected = await checkRemoteServer();
  if (isConnected) {
    log('INFO', 'Successfully connected to remote server');
  } else {
    log('WARN', 'Could not connect to remote server - make sure it is running');
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('INFO', 'Shutting down MCP client...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('INFO', 'Shutting down MCP client...');
  process.exit(0);
});

main().catch((error) => {
  log('ERROR', 'Failed to start MCP client:', error);
  process.exit(1);
});
