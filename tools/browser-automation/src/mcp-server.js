#!/usr/bin/env node

/**
 * MCP Server for Playwright Browser Automation Tool
 * Provides browser automation capabilities through the Model Context Protocol
 */

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { chromium } from 'playwright';
import fs from 'fs/promises';
import path from 'path';
import { config, getChromeEndpoint } from './config.js';
import { 
  retryWithBackoff, 
  withTimeout, 
  AutomationError, 
  ConnectionError,
  ErrorTypes,
  reportError
} from './utils/errorHandler.js';

// Logger utility (to stderr to avoid interfering with MCP protocol)
const log = (level, message, data) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [MCP-SERVER] [${level}] ${message}`;
  if (data) {
    console.error(logMessage, JSON.stringify(data, null, 2));
  } else {
    console.error(logMessage);
  }
};

// Create MCP server
const server = new Server(
  {
    name: 'browser-automation-mcp',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

log('INFO', 'Browser Automation MCP Server initialized');

// Global browser connection
let globalBrowser = null;

/**
 * Connect to Chrome browser via CDP
 */
async function connectToChrome() {
  if (globalBrowser) {
    try {
      // Test if existing connection is still valid
      const contexts = globalBrowser.contexts();
      return globalBrowser;
    } catch (error) {
      log('WARN', 'Existing browser connection invalid, reconnecting');
      globalBrowser = null;
    }
  }

  return retryWithBackoff(async () => {
    log('INFO', 'Attempting to connect to Chrome...');
    
    // Check if Chrome is available
    try {
      const response = await fetch(`${getChromeEndpoint()}/json/version`);
      if (!response.ok) {
        throw new ConnectionError('Chrome remote debugging not available');
      }
    } catch (error) {
      throw new ConnectionError(
        'Chrome remote debugging not available. Make sure Chrome is running with: chrome --remote-debugging-port=9222'
      );
    }

    // Connect to Chrome with timeout
    const browser = await withTimeout(
      chromium.connectOverCDP(getChromeEndpoint()),
      config.chrome.connectionTimeout,
      'Chrome connection timed out'
    );
    
    globalBrowser = browser;
    log('INFO', 'Successfully connected to Chrome browser');
    return browser;
    
  }, config.chrome.maxRetries, config.chrome.retryDelay);
}

/**
 * Get the active page or create a new one
 */
async function getActivePage(browser) {
  const contexts = browser.contexts();
  if (contexts.length === 0) {
    throw new AutomationError('No browser contexts found', ErrorTypes.TAB_NOT_FOUND);
  }
  
  const context = contexts[0];
  const pages = context.pages();
  
  if (pages.length === 0) {
    // Create a new page if none exist
    return await context.newPage();
  }
  
  // Return the first page and bring it to front
  const page = pages[0];
  await page.bringToFront();
  return page;
}

/**
 * Take a screenshot
 */
async function takeScreenshot(page, options = {}) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = options.filename || `mcp-screenshot-${timestamp}.png`;
  
  // Ensure screenshots directory exists
  await fs.mkdir('./screenshots', { recursive: true });
  
  const filepath = path.join('./screenshots', filename);
  
  const screenshotOptions = {
    path: filepath,
    fullPage: options.fullPage !== false
  };
  
  // Only add quality for JPEG files
  if (filepath.toLowerCase().endsWith('.jpg') || filepath.toLowerCase().endsWith('.jpeg')) {
    screenshotOptions.quality = options.quality || 90;
  }
  
  await page.screenshot(screenshotOptions);
  
  return {
    success: true,
    filepath,
    message: `Screenshot saved: ${filepath}`
  };
}

/**
 * Execute a task from the tasks directory
 */
async function executeTask(page, taskName, options = {}) {
  const currentDir = path.dirname(new URL(import.meta.url).pathname);
  const taskPath = path.resolve(currentDir, 'tasks', `${taskName}.js`);
  
  try {
    const taskModule = await import(`file://${taskPath}`);
    if (typeof taskModule.default === 'function') {
      log('INFO', `Executing task: ${taskName}`);
      await taskModule.default(page, options);
      return {
        success: true,
        message: `Task completed: ${taskName}`
      };
    } else {
      throw new Error('Task module must export a default function');
    }
  } catch (error) {
    log('ERROR', `Failed to execute task ${taskName}:`, error.message);
    throw error;
  }
}

// Define tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  log('DEBUG', 'Received ListTools request');
  return {
    tools: [
      {
        name: 'browser_screenshot',
        description: 'Take a screenshot of the current page or navigate to a URL and take a screenshot',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL to navigate to before taking screenshot (optional)',
            },
            filename: {
              type: 'string',
              description: 'Custom filename for the screenshot (optional)',
            },
            fullPage: {
              type: 'boolean',
              description: 'Capture full scrollable page (not just visible viewport)',
              default: true,
            },
            quality: {
              type: 'number',
              description: 'Image quality for JPEG files (1-100)',
              default: 90,
            },
          },
        },
      },
      {
        name: 'browser_navigate',
        description: 'Navigate to a URL and wait for it to load',
        inputSchema: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description: 'URL to navigate to',
            },
            waitUntil: {
              type: 'string',
              description: 'When to consider navigation complete',
              enum: ['load', 'domcontentloaded', 'networkidle'],
              default: 'networkidle',
            },
          },
          required: ['url'],
        },
      },
      {
        name: 'browser_execute',
        description: 'Execute JavaScript code on the current page and return the result',
        inputSchema: {
          type: 'object',
          properties: {
            code: {
              type: 'string',
              description: 'JavaScript code to execute',
            },
            url: {
              type: 'string',
              description: 'URL to navigate to before executing code (optional)',
            },
          },
          required: ['code'],
        },
      },
      {
        name: 'browser_task',
        description: 'Execute a predefined automation task (submitForm, extractData, interactiveDemo)',
        inputSchema: {
          type: 'object',
          properties: {
            taskName: {
              type: 'string',
              description: 'Name of the task to execute',
              enum: ['submitForm', 'extractData', 'interactiveDemo'],
            },
            url: {
              type: 'string',
              description: 'URL to navigate to before executing task (optional)',
            },
            options: {
              type: 'object',
              description: 'Task-specific options (optional)',
            },
          },
          required: ['taskName'],
        },
      },
      {
        name: 'browser_get_page_info',
        description: 'Get information about the current page (title, URL, element counts, etc.)',
        inputSchema: {
          type: 'object',
          properties: {
            includeContent: {
              type: 'boolean',
              description: 'Include page content in the response',
              default: false,
            },
          },
        },
      },
    ],
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  log('INFO', `Received tool call: ${name}`, args);

  try {
    // Connect to Chrome
    const browser = await connectToChrome();
    const page = await getActivePage(browser);

    switch (name) {
      case 'browser_screenshot': {
        // Navigate if URL provided
        if (args.url) {
          log('INFO', `Navigating to: ${args.url}`);
          await page.goto(args.url, { 
            waitUntil: 'networkidle',
            timeout: 30000 
          });
        }

        const result = await takeScreenshot(page, args);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      case 'browser_navigate': {
        log('INFO', `Navigating to: ${args.url}`);
        await page.goto(args.url, { 
          waitUntil: args.waitUntil || 'networkidle',
          timeout: 30000 
        });

        const title = await page.title();
        const url = page.url();

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: true,
                title,
                url,
                message: `Successfully navigated to ${url}`
              }, null, 2),
            },
          ],
        };
      }

      case 'browser_execute': {
        // Navigate if URL provided
        if (args.url) {
          log('INFO', `Navigating to: ${args.url}`);
          await page.goto(args.url, { 
            waitUntil: 'networkidle',
            timeout: 30000 
          });
        }

        log('INFO', 'Executing JavaScript code...');
        const result = await withTimeout(
          page.evaluate(args.code),
          30000,
          'JavaScript execution timed out'
        );

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                success: true,
                result,
                message: 'JavaScript executed successfully'
              }, null, 2),
            },
          ],
        };
      }

      case 'browser_task': {
        // Navigate if URL provided
        if (args.url) {
          log('INFO', `Navigating to: ${args.url}`);
          await page.goto(args.url, { 
            waitUntil: 'networkidle',
            timeout: 30000 
          });
        }

        const result = await executeTask(page, args.taskName, args.options || {});
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      case 'browser_get_page_info': {
        const pageInfo = await page.evaluate((includeContent) => {
          const info = {
            title: document.title,
            url: window.location.href,
            domain: window.location.hostname,
            viewport: {
              width: window.innerWidth,
              height: window.innerHeight
            },
            documentSize: {
              width: document.documentElement.scrollWidth,
              height: document.documentElement.scrollHeight
            },
            elementCounts: {
              total: document.querySelectorAll('*').length,
              links: document.querySelectorAll('a').length,
              images: document.querySelectorAll('img').length,
              forms: document.querySelectorAll('form').length,
              buttons: document.querySelectorAll('button').length
            }
          };

          if (includeContent) {
            info.content = {
              headings: Array.from(document.querySelectorAll('h1,h2,h3,h4,h5,h6')).map(h => ({
                tag: h.tagName.toLowerCase(),
                text: h.textContent?.trim()
              })),
              paragraphs: Array.from(document.querySelectorAll('p')).slice(0, 5).map(p => p.textContent?.trim()),
              links: Array.from(document.querySelectorAll('a[href]')).slice(0, 10).map(a => ({
                text: a.textContent?.trim(),
                href: a.href
              }))
            };
          }

          return info;
        }, args.includeContent || false);

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(pageInfo, null, 2),
            },
          ],
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    log('ERROR', `Tool execution failed: ${name}`, error);
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            success: false,
            error: error.message,
            type: error.type || 'UNKNOWN',
            details: error.details || {}
          }, null, 2),
        },
      ],
      isError: true,
    };
  }
});

// Start the server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  log('INFO', 'Browser Automation MCP Server started');
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('INFO', 'Shutting down MCP server...');
  if (globalBrowser) {
    try {
      await globalBrowser.close();
    } catch (error) {
      log('WARN', 'Error closing browser:', error.message);
    }
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log('INFO', 'Shutting down MCP server...');
  if (globalBrowser) {
    try {
      await globalBrowser.close();
    } catch (error) {
      log('WARN', 'Error closing browser:', error.message);
    }
  }
  process.exit(0);
});

main().catch((error) => {
  log('ERROR', 'Failed to start MCP server:', error);
  process.exit(1);
});
