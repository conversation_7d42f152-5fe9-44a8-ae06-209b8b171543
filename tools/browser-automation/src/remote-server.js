#!/usr/bin/env node

/**
 * Remote Browser Automation Server
 * Runs on port 3636 and provides browser automation capabilities via HTTP API
 * Similar to the mcp-backend architecture but for browser automation
 */

import express from 'express';
import cors from 'cors';
import { chromium } from 'playwright';
import fs from 'fs/promises';
import path from 'path';
import { config, getChromeEndpoint } from './config.js';
import { 
  retryWithBackoff, 
  withTimeout, 
  AutomationError, 
  ConnectionError,
  ErrorTypes,
  reportError
} from './utils/errorHandler.js';

const app = express();
const PORT = process.env.PORT || 3636;

// Middleware
app.use(cors());
app.use(express.json());

// Logger utility
const log = (level, message, data) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [REMOTE-SERVER] [${level}] ${message}`;
  if (data) {
    console.log(logMessage, JSON.stringify(data, null, 2));
  } else {
    console.log(logMessage);
  }
};

// Global browser connection
let globalBrowser = null;

/**
 * Connect to Chrome browser via CDP
 */
async function connectToChrome() {
  if (globalBrowser) {
    try {
      // Test if existing connection is still valid
      const contexts = globalBrowser.contexts();
      return globalBrowser;
    } catch (error) {
      log('WARN', 'Existing browser connection invalid, reconnecting');
      globalBrowser = null;
    }
  }

  return retryWithBackoff(async () => {
    log('INFO', 'Attempting to connect to Chrome...');
    
    // Check if Chrome is available
    try {
      const response = await fetch(`${getChromeEndpoint()}/json/version`);
      if (!response.ok) {
        throw new ConnectionError('Chrome remote debugging not available');
      }
    } catch (error) {
      throw new ConnectionError(
        'Chrome remote debugging not available. Make sure Chrome is running with: chrome --remote-debugging-port=9222'
      );
    }

    // Connect to Chrome with timeout
    const browser = await withTimeout(
      chromium.connectOverCDP(getChromeEndpoint()),
      config.chrome.connectionTimeout,
      'Chrome connection timed out'
    );
    
    globalBrowser = browser;
    log('INFO', 'Successfully connected to Chrome browser');
    return browser;
    
  }, config.chrome.maxRetries, config.chrome.retryDelay);
}

/**
 * Get the active page or create a new one
 */
async function getActivePage(browser) {
  const contexts = browser.contexts();
  if (contexts.length === 0) {
    throw new AutomationError('No browser contexts found', ErrorTypes.TAB_NOT_FOUND);
  }
  
  const context = contexts[0];
  const pages = context.pages();
  
  if (pages.length === 0) {
    // Create a new page if none exist
    return await context.newPage();
  }
  
  // Return the first page and bring it to front
  const page = pages[0];
  await page.bringToFront();
  return page;
}

/**
 * Take a screenshot
 */
async function takeScreenshot(page, options = {}) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = options.filename || `remote-screenshot-${timestamp}.png`;
  
  // Ensure screenshots directory exists
  await fs.mkdir('./screenshots', { recursive: true });
  
  const filepath = path.join('./screenshots', filename);
  
  const screenshotOptions = {
    path: filepath,
    fullPage: options.fullPage !== false
  };
  
  // Only add quality for JPEG files
  if (filepath.toLowerCase().endsWith('.jpg') || filepath.toLowerCase().endsWith('.jpeg')) {
    screenshotOptions.quality = options.quality || 90;
  }
  
  await page.screenshot(screenshotOptions);
  
  return {
    success: true,
    filepath,
    message: `Screenshot saved: ${filepath}`
  };
}

/**
 * Execute a task from the tasks directory
 */
async function executeTask(page, taskName, options = {}) {
  const currentDir = path.dirname(new URL(import.meta.url).pathname);
  const taskPath = path.resolve(currentDir, 'tasks', `${taskName}.js`);
  
  try {
    const taskModule = await import(`file://${taskPath}`);
    if (typeof taskModule.default === 'function') {
      log('INFO', `Executing task: ${taskName}`);
      await taskModule.default(page, options);
      return {
        success: true,
        message: `Task completed: ${taskName}`
      };
    } else {
      throw new Error('Task module must export a default function');
    }
  } catch (error) {
    log('ERROR', `Failed to execute task ${taskName}:`, error.message);
    throw error;
  }
}

// API Routes

// Health check
app.get('/api/status', (req, res) => {
  res.json({
    status: 'running',
    service: 'browser-automation-remote-server',
    port: PORT,
    timestamp: new Date().toISOString(),
    browserConnected: globalBrowser !== null
  });
});

// Screenshot endpoint
app.post('/api/screenshot', async (req, res) => {
  try {
    log('INFO', 'Screenshot request received', req.body);
    
    const browser = await connectToChrome();
    const page = await getActivePage(browser);
    
    // Navigate if URL provided
    if (req.body.url) {
      log('INFO', `Navigating to: ${req.body.url}`);
      await page.goto(req.body.url, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
    }

    const result = await takeScreenshot(page, req.body);
    res.json(result);
    
  } catch (error) {
    log('ERROR', 'Screenshot failed', error);
    res.status(500).json({
      success: false,
      error: error.message,
      type: error.type || 'UNKNOWN'
    });
  }
});

// Navigate endpoint
app.post('/api/navigate', async (req, res) => {
  try {
    log('INFO', 'Navigate request received', req.body);
    
    const { url, waitUntil = 'networkidle' } = req.body;
    
    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'URL is required'
      });
    }
    
    const browser = await connectToChrome();
    const page = await getActivePage(browser);
    
    log('INFO', `Navigating to: ${url}`);
    await page.goto(url, { 
      waitUntil,
      timeout: 30000 
    });

    const title = await page.title();
    const currentUrl = page.url();

    res.json({
      success: true,
      title,
      url: currentUrl,
      message: `Successfully navigated to ${currentUrl}`
    });
    
  } catch (error) {
    log('ERROR', 'Navigation failed', error);
    res.status(500).json({
      success: false,
      error: error.message,
      type: error.type || 'UNKNOWN'
    });
  }
});

// Execute JavaScript endpoint
app.post('/api/execute', async (req, res) => {
  try {
    log('INFO', 'Execute request received', req.body);
    
    const { code, url } = req.body;
    
    if (!code) {
      return res.status(400).json({
        success: false,
        error: 'JavaScript code is required'
      });
    }
    
    const browser = await connectToChrome();
    const page = await getActivePage(browser);
    
    // Navigate if URL provided
    if (url) {
      log('INFO', `Navigating to: ${url}`);
      await page.goto(url, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
    }

    log('INFO', 'Executing JavaScript code...');
    const result = await withTimeout(
      page.evaluate(code),
      30000,
      'JavaScript execution timed out'
    );

    res.json({
      success: true,
      result,
      message: 'JavaScript executed successfully'
    });
    
  } catch (error) {
    log('ERROR', 'JavaScript execution failed', error);
    res.status(500).json({
      success: false,
      error: error.message,
      type: error.type || 'UNKNOWN'
    });
  }
});

// Task execution endpoint
app.post('/api/task', async (req, res) => {
  try {
    log('INFO', 'Task request received', req.body);
    
    const { taskName, url, options = {} } = req.body;
    
    if (!taskName) {
      return res.status(400).json({
        success: false,
        error: 'Task name is required'
      });
    }
    
    const browser = await connectToChrome();
    const page = await getActivePage(browser);
    
    // Navigate if URL provided
    if (url) {
      log('INFO', `Navigating to: ${url}`);
      await page.goto(url, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
    }

    const result = await executeTask(page, taskName, options);
    res.json(result);
    
  } catch (error) {
    log('ERROR', 'Task execution failed', error);
    res.status(500).json({
      success: false,
      error: error.message,
      type: error.type || 'UNKNOWN'
    });
  }
});

// Page info endpoint
app.get('/api/page-info', async (req, res) => {
  try {
    log('INFO', 'Page info request received');
    
    const includeContent = req.query.includeContent === 'true';
    
    const browser = await connectToChrome();
    const page = await getActivePage(browser);
    
    const pageInfo = await page.evaluate((includeContent) => {
      const info = {
        title: document.title,
        url: window.location.href,
        domain: window.location.hostname,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        documentSize: {
          width: document.documentElement.scrollWidth,
          height: document.documentElement.scrollHeight
        },
        elementCounts: {
          total: document.querySelectorAll('*').length,
          links: document.querySelectorAll('a').length,
          images: document.querySelectorAll('img').length,
          forms: document.querySelectorAll('form').length,
          buttons: document.querySelectorAll('button').length
        }
      };

      if (includeContent) {
        info.content = {
          headings: Array.from(document.querySelectorAll('h1,h2,h3,h4,h5,h6')).map(h => ({
            tag: h.tagName.toLowerCase(),
            text: h.textContent?.trim()
          })),
          paragraphs: Array.from(document.querySelectorAll('p')).slice(0, 5).map(p => p.textContent?.trim()),
          links: Array.from(document.querySelectorAll('a[href]')).slice(0, 10).map(a => ({
            text: a.textContent?.trim(),
            href: a.href
          }))
        };
      }

      return info;
    }, includeContent);

    res.json(pageInfo);
    
  } catch (error) {
    log('ERROR', 'Page info failed', error);
    res.status(500).json({
      success: false,
      error: error.message,
      type: error.type || 'UNKNOWN'
    });
  }
});

// Error handler
app.use((error, req, res, next) => {
  log('ERROR', 'Unhandled error', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: error.message
  });
});

// Start server
app.listen(PORT, () => {
  log('INFO', `Browser Automation Remote Server started on port ${PORT}`);
  log('INFO', 'Available endpoints:');
  log('INFO', '  GET  /api/status - Health check');
  log('INFO', '  POST /api/screenshot - Take screenshots');
  log('INFO', '  POST /api/navigate - Navigate to URLs');
  log('INFO', '  POST /api/execute - Execute JavaScript');
  log('INFO', '  POST /api/task - Execute automation tasks');
  log('INFO', '  GET  /api/page-info - Get page information');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  log('INFO', 'Shutting down remote server...');
  if (globalBrowser) {
    try {
      await globalBrowser.close();
    } catch (error) {
      log('WARN', 'Error closing browser:', error.message);
    }
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  log('INFO', 'Shutting down remote server...');
  if (globalBrowser) {
    try {
      await globalBrowser.close();
    } catch (error) {
      log('WARN', 'Error closing browser:', error.message);
    }
  }
  process.exit(0);
});
