#!/bin/bash

# Script to start Chrome with remote debugging enabled
# This makes it easy to launch Chrome with the correct parameters

set -e

# Configuration
DEBUG_PORT=9222
USER_DATA_DIR="${CHROME_PROFILE_DIR:-$HOME/.chrome-automation}"

# Allow custom profile directory via environment variable
# Usage: CHROME_PROFILE_DIR="/path/to/profile" ./start-chrome.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Chrome with Remote Debugging...${NC}"

# Check if Chrome is already running on the debug port
if lsof -i :$DEBUG_PORT >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Chrome is already running on port $DEBUG_PORT${NC}"
    echo -e "${BLUE}ℹ️  Testing if it's accessible...${NC}"

    # Test if the debugging endpoint is working
    if curl -s "http://localhost:$DEBUG_PORT/json/version" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Chrome debugging is already working!${NC}"
        echo -e "${BLUE}🔗 Debug endpoint: http://localhost:$DEBUG_PORT/json/version${NC}"
        echo -e "${BLUE}🎯 You can proceed with automation commands${NC}"
        exit 0
    else
        echo -e "${RED}❌ Port is in use but Chrome debugging is not responding${NC}"
        echo -e "${YELLOW}💡 Try killing the process: pkill -f 'remote-debugging-port=$DEBUG_PORT'${NC}"
        exit 1
    fi
fi

# Create user data directory if it doesn't exist
mkdir -p "$USER_DATA_DIR"

# Detect Chrome executable
CHROME_EXEC=""

if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CHROME_EXEC="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if [[ ! -f "$CHROME_EXEC" ]]; then
        CHROME_EXEC="/Applications/Chromium.app/Contents/MacOS/Chromium"
    fi
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    CHROME_EXEC=$(which google-chrome || which chromium-browser || which chromium || echo "")
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    # Windows
    CHROME_EXEC="C:/Program Files/Google/Chrome/Application/chrome.exe"
    if [[ ! -f "$CHROME_EXEC" ]]; then
        CHROME_EXEC="C:/Program Files (x86)/Google/Chrome/Application/chrome.exe"
    fi
fi

# Check if Chrome executable was found
if [[ -z "$CHROME_EXEC" ]] || [[ ! -f "$CHROME_EXEC" ]]; then
    echo -e "${RED}❌ Chrome executable not found!${NC}"
    echo -e "${YELLOW}Please install Chrome or Chromium, or set the path manually${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Found Chrome: $CHROME_EXEC${NC}"

# Chrome arguments
CHROME_ARGS=(
    "--remote-debugging-port=$DEBUG_PORT"
    "--user-data-dir=$USER_DATA_DIR"
    "--no-first-run"
    "--no-default-browser-check"
    "--disable-background-timer-throttling"
    "--disable-backgrounding-occluded-windows"
    "--disable-renderer-backgrounding"
)

# Add additional arguments if provided
if [[ $# -gt 0 ]]; then
    CHROME_ARGS+=("$@")
fi

echo -e "${BLUE}📋 Chrome arguments:${NC}"
printf '  %s\n' "${CHROME_ARGS[@]}"

echo -e "${BLUE}🌐 Debug endpoint will be: http://localhost:$DEBUG_PORT${NC}"
echo -e "${BLUE}📁 User data directory: $USER_DATA_DIR${NC}"
echo -e "${GREEN}💾 Profile persistence: Login sessions will be saved and restored${NC}"

# Check if this is a new profile
if [[ ! -d "$USER_DATA_DIR" ]]; then
    echo -e "${YELLOW}📝 Creating new Chrome profile directory${NC}"
    echo -e "${BLUE}ℹ️  Your login sessions will be saved for future use${NC}"
else
    echo -e "${GREEN}🔄 Using existing Chrome profile (logins preserved)${NC}"
fi

# Start Chrome
echo -e "${GREEN}🚀 Launching Chrome...${NC}"

if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS - use open to start in background
    open -a "$CHROME_EXEC" --args "${CHROME_ARGS[@]}" &
else
    # Linux/Windows - start directly
    "$CHROME_EXEC" "${CHROME_ARGS[@]}" &
fi

CHROME_PID=$!

# Wait a moment for Chrome to start
sleep 3

# Verify Chrome started successfully
if lsof -i :$DEBUG_PORT >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Chrome started successfully!${NC}"
    echo -e "${BLUE}🔗 Debug endpoint: http://localhost:$DEBUG_PORT/json/version${NC}"
    echo -e "${BLUE}🎯 You can now run automation commands${NC}"
    echo ""
    echo -e "${GREEN}💾 Profile Information:${NC}"
    echo -e "${BLUE}  • Profile location: $USER_DATA_DIR${NC}"
    echo -e "${BLUE}  • Login sessions: Will be preserved between restarts${NC}"
    echo -e "${BLUE}  • Extensions: Will be maintained${NC}"
    echo -e "${BLUE}  • Bookmarks: Will be saved${NC}"
    echo ""
    echo -e "${YELLOW}Example commands:${NC}"
    echo "  npm test                    # Test the connection"
    echo "  node src/automate.js --screenshot"
    echo "  node src/automate.js --task interactiveDemo"
    echo ""
    echo -e "${BLUE}💡 To stop Chrome debugging, close all Chrome windows or run:${NC}"
    echo "  pkill -f 'remote-debugging-port=$DEBUG_PORT'"
    echo ""
    echo -e "${YELLOW}🔧 Profile Management:${NC}"
    echo "  • To use a different profile: CHROME_PROFILE_DIR='/path/to/profile' ./start-chrome.sh"
    echo "  • To reset profile: rm -rf '$USER_DATA_DIR' && ./start-chrome.sh"
    echo "  • Profile backup: cp -r '$USER_DATA_DIR' '$USER_DATA_DIR.backup'"
else
    echo -e "${RED}❌ Failed to start Chrome with debugging enabled${NC}"
    echo -e "${YELLOW}Check the Chrome process and try again${NC}"
    exit 1
fi
