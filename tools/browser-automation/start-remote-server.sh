#!/bin/bash

# Start the Browser Automation Remote Server
# This server runs on port 3636 and provides HTTP API endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Browser Automation Remote Server...${NC}"

# Check if Chrome is running
echo -e "${BLUE}📋 Checking Chrome debugging connection...${NC}"
if ! curl -s http://localhost:9222/json/version >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Chrome is not running with debugging enabled${NC}"
    echo -e "${YELLOW}Starting Chrome automatically...${NC}"
    ./start-chrome.sh &
    sleep 3
    
    if ! curl -s http://localhost:9222/json/version >/dev/null 2>&1; then
        echo -e "${RED}❌ Failed to start Chrome with debugging${NC}"
        echo -e "${YELLOW}Please start Chrome manually with: ./start-chrome.sh${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Chrome debugging is available${NC}"

# Check if port 3636 is already in use
if lsof -i :3636 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Port 3636 is already in use${NC}"
    echo -e "${BLUE}Checking if it's our server...${NC}"
    
    if curl -s http://localhost:3636/api/status | grep -q "browser-automation-remote-server"; then
        echo -e "${GREEN}✅ Browser Automation Remote Server is already running${NC}"
        echo -e "${BLUE}🔗 Server URL: http://localhost:3636${NC}"
        exit 0
    else
        echo -e "${RED}❌ Port 3636 is used by another service${NC}"
        echo -e "${YELLOW}Please stop the other service or change the port${NC}"
        exit 1
    fi
fi

# Start the remote server
echo -e "${BLUE}🌐 Starting remote server on port 3636...${NC}"

# Run the server
npm run remote-server
