#!/bin/bash

# Test script for the Browser Automation MCP Server
# This script tests the MCP server functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Browser Automation MCP Server...${NC}"

# Check if Chrome is running
echo -e "${BLUE}📋 Checking Chrome debugging connection...${NC}"
if ! curl -s http://localhost:9222/json/version >/dev/null 2>&1; then
    echo -e "${RED}❌ Chrome is not running with debugging enabled${NC}"
    echo -e "${YELLOW}Please start Chrome with: ./start-chrome.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Chrome debugging is available${NC}"

# Test MCP server startup
echo -e "${BLUE}🚀 Testing MCP server startup...${NC}"

# Create a simple test that sends a list tools request
cat > /tmp/mcp-test.json << 'EOF'
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list"
}
EOF

echo -e "${BLUE}📝 Sending list tools request...${NC}"

# Test the MCP server with a simple request
timeout 5s bash -c '
    echo "{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"tools/list\"}" | node src/mcp-server.js
' > /tmp/mcp-response.json 2>/tmp/mcp-error.log &

MCP_PID=$!
sleep 2

# Check if the process started and is logging correctly
if [ -f /tmp/mcp-error.log ] && grep -q "MCP-SERVER.*INFO.*initialized" /tmp/mcp-error.log; then
    echo -e "${GREEN}✅ MCP server started successfully${NC}"
    kill $MCP_PID 2>/dev/null || true
    wait $MCP_PID 2>/dev/null || true
else
    echo -e "${RED}❌ MCP server failed to start properly${NC}"
    if [ -f /tmp/mcp-error.log ]; then
        echo -e "${YELLOW}Error log:${NC}"
        cat /tmp/mcp-error.log
    fi
    exit 1
fi

# Check if we got a response
if [ -f /tmp/mcp-response.json ] && [ -s /tmp/mcp-response.json ]; then
    echo -e "${GREEN}✅ MCP server responded to tools/list request${NC}"
    echo -e "${BLUE}Response preview:${NC}"
    head -5 /tmp/mcp-response.json
else
    echo -e "${YELLOW}⚠️  No response received (this might be normal for stdio transport)${NC}"
fi

# Test basic functionality
echo -e "${BLUE}🔧 Testing basic MCP server functionality...${NC}"

# Create a more comprehensive test
cat > /tmp/test-mcp-server.js << 'EOF'
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

async function testMCPServer() {
    console.log('Starting MCP server test...');
    
    const mcpProcess = spawn('node', ['src/mcp-server.js'], {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let responseReceived = false;
    let errorOccurred = false;
    
    mcpProcess.stdout.on('data', (data) => {
        console.log('MCP Response:', data.toString());
        responseReceived = true;
    });
    
    mcpProcess.stderr.on('data', (data) => {
        const message = data.toString();
        if (message.includes('ERROR')) {
            console.error('MCP Error:', message);
            errorOccurred = true;
        } else {
            console.log('MCP Log:', message);
        }
    });
    
    // Send a list tools request
    const request = {
        jsonrpc: "2.0",
        id: 1,
        method: "tools/list"
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    // Wait for response
    await setTimeout(3000);
    
    mcpProcess.kill();
    
    if (errorOccurred) {
        console.log('❌ MCP server encountered errors');
        process.exit(1);
    } else {
        console.log('✅ MCP server test completed');
    }
}

testMCPServer().catch(console.error);
EOF

echo -e "${BLUE}🧪 Running comprehensive MCP test...${NC}"
node /tmp/test-mcp-server.js

echo -e "${GREEN}✅ MCP server tests completed successfully!${NC}"

# Cleanup
rm -f /tmp/mcp-test.json /tmp/mcp-response.json /tmp/mcp-error.log /tmp/test-mcp-server.js

echo -e "${BLUE}📋 MCP Server Summary:${NC}"
echo "  • MCP server can be started with: npm run mcp"
echo "  • Available tools: browser_screenshot, browser_navigate, browser_execute, browser_task, browser_get_page_info"
echo "  • Requires Chrome running with debugging: ./start-chrome.sh"
echo ""
echo -e "${GREEN}🎉 Browser Automation MCP Server is ready to use!${NC}"
