#!/bin/bash

# Test script for the Remote Browser Automation Setup
# Tests both the remote server and MCP client

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Remote Browser Automation Setup...${NC}"

# Check if Chrome is running
echo -e "${BLUE}📋 Step 1: Checking Chrome debugging connection...${NC}"
if ! curl -s http://localhost:9222/json/version >/dev/null 2>&1; then
    echo -e "${RED}❌ Chrome is not running with debugging enabled${NC}"
    echo -e "${YELLOW}Please start Chrome with: ./start-chrome.sh${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Chrome debugging is available${NC}"

# Test remote server startup
echo -e "${BLUE}🚀 Step 2: Testing remote server startup...${NC}"

# Start remote server in background
npm run remote-server &
SERVER_PID=$!

# Wait for server to start
sleep 3

# Check if server is running
if ! kill -0 $SERVER_PID 2>/dev/null; then
    echo -e "${RED}❌ Remote server failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Remote server started (PID: $SERVER_PID)${NC}"

# Test server endpoints
echo -e "${BLUE}🔧 Step 3: Testing server endpoints...${NC}"

# Test status endpoint
echo -e "${BLUE}Testing /api/status...${NC}"
if curl -s http://localhost:3636/api/status | grep -q "browser-automation-remote-server"; then
    echo -e "${GREEN}✅ Status endpoint working${NC}"
else
    echo -e "${RED}❌ Status endpoint failed${NC}"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Test page info endpoint
echo -e "${BLUE}Testing /api/page-info...${NC}"
if curl -s http://localhost:3636/api/page-info | grep -q "title"; then
    echo -e "${GREEN}✅ Page info endpoint working${NC}"
else
    echo -e "${RED}❌ Page info endpoint failed${NC}"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Test screenshot endpoint
echo -e "${BLUE}Testing /api/screenshot...${NC}"
SCREENSHOT_RESULT=$(curl -s -X POST http://localhost:3636/api/screenshot \
    -H "Content-Type: application/json" \
    -d '{"filename": "test-remote.png"}')

if echo "$SCREENSHOT_RESULT" | grep -q "success.*true"; then
    echo -e "${GREEN}✅ Screenshot endpoint working${NC}"
else
    echo -e "${RED}❌ Screenshot endpoint failed${NC}"
    echo "Response: $SCREENSHOT_RESULT"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

# Test MCP client
echo -e "${BLUE}🤖 Step 4: Testing MCP client...${NC}"

# Create a simple MCP test
cat > /tmp/test-mcp-client.js << 'EOF'
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

async function testMCPClient() {
    console.log('Starting MCP client test...');
    
    const mcpProcess = spawn('node', ['src/mcp-client.js'], {
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let responseReceived = false;
    let errorOccurred = false;
    
    mcpProcess.stdout.on('data', (data) => {
        console.log('MCP Response:', data.toString());
        responseReceived = true;
    });
    
    mcpProcess.stderr.on('data', (data) => {
        const message = data.toString();
        if (message.includes('ERROR')) {
            console.error('MCP Error:', message);
            errorOccurred = true;
        } else {
            console.log('MCP Log:', message);
        }
    });
    
    // Send a list tools request
    const request = {
        jsonrpc: "2.0",
        id: 1,
        method: "tools/list"
    };
    
    mcpProcess.stdin.write(JSON.stringify(request) + '\n');
    
    // Wait for response
    await setTimeout(3000);
    
    mcpProcess.kill();
    
    if (errorOccurred) {
        console.log('❌ MCP client encountered errors');
        process.exit(1);
    } else {
        console.log('✅ MCP client test completed');
    }
}

testMCPClient().catch(console.error);
EOF

echo -e "${BLUE}Running MCP client test...${NC}"
node /tmp/test-mcp-client.js

echo -e "${GREEN}✅ MCP client test completed${NC}"

# Cleanup
echo -e "${BLUE}🧹 Step 5: Cleaning up...${NC}"
kill $SERVER_PID 2>/dev/null || true
wait $SERVER_PID 2>/dev/null || true
rm -f /tmp/test-mcp-client.js

echo -e "${GREEN}✅ All tests passed!${NC}"

echo -e "${BLUE}📋 Remote Setup Summary:${NC}"
echo "  • Remote server can be started with: npm run remote-server"
echo "  • MCP client can be started with: npm run mcp-client"
echo "  • Server runs on: http://localhost:3636"
echo "  • Available endpoints: /api/status, /api/screenshot, /api/navigate, /api/execute, /api/task, /api/page-info"
echo ""
echo -e "${GREEN}🎉 Remote Browser Automation Setup is ready to use!${NC}"
