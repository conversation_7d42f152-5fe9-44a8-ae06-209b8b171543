#!/usr/bin/env node

/**
 * Test script to verify Chrome connection and basic functionality
 */

import { chromium } from 'playwright';
import chalk from 'chalk';
import { config, getChromeEndpoint } from '../config.js';
import { 
  retryWithBackoff, 
  withTimeout, 
  AutomationError, 
  ErrorTypes,
  reportError 
} from '../utils/errorHandler.js';

const log = {
  info: (msg, ...args) => console.log(chalk.blue('[TEST]'), msg, ...args),
  success: (msg, ...args) => console.log(chalk.green('[TEST]'), msg, ...args),
  error: (msg, ...args) => console.log(chalk.red('[TEST]'), msg, ...args),
  warn: (msg, ...args) => console.log(chalk.yellow('[TEST]'), msg, ...args)
};

async function testChromeConnection() {
  log.info('Testing Chrome remote debugging connection...');
  
  try {
    // Test 1: Check if Chrome debugging endpoint is available
    log.info('Step 1: Checking Chrome debugging endpoint...');
    const response = await fetch(`${getChromeEndpoint()}/json/version`);
    
    if (!response.ok) {
      throw new AutomationError(
        `Chrome debugging endpoint returned ${response.status}`,
        ErrorTypes.CONNECTION_FAILED
      );
    }
    
    const versionInfo = await response.json();
    log.success('Chrome debugging endpoint is available');
    log.info('Chrome version:', versionInfo.Browser);
    log.info('WebSocket URL:', versionInfo.webSocketDebuggerUrl);
    
    // Test 2: Connect to Chrome via Playwright
    log.info('Step 2: Connecting to Chrome via Playwright...');
    const browser = await withTimeout(
      chromium.connectOverCDP(getChromeEndpoint()),
      config.chrome.connectionTimeout,
      'Failed to connect to Chrome within timeout'
    );
    
    log.success('Successfully connected to Chrome via Playwright');
    
    // Test 3: List available contexts and pages
    log.info('Step 3: Discovering browser contexts and pages...');
    const contexts = browser.contexts();
    log.info(`Found ${contexts.length} browser context(s)`);
    
    let totalPages = 0;
    for (let i = 0; i < contexts.length; i++) {
      const context = contexts[i];
      const pages = context.pages();
      totalPages += pages.length;
      log.info(`  Context ${i}: ${pages.length} page(s)`);
      
      for (let j = 0; j < Math.min(pages.length, 3); j++) {
        const page = pages[j];
        try {
          const title = await page.title();
          const url = page.url();
          log.info(`    Page ${j}: "${title}" - ${url}`);
        } catch (error) {
          log.warn(`    Page ${j}: Unable to get info - ${error.message}`);
        }
      }
    }
    
    if (totalPages === 0) {
      log.warn('No pages found. Consider opening some tabs in Chrome.');
    } else {
      log.success(`Found ${totalPages} total page(s)`);
    }
    
    // Test 4: Basic page interaction (if pages available)
    if (totalPages > 0) {
      log.info('Step 4: Testing basic page interaction...');
      const firstPage = contexts[0].pages()[0];
      
      try {
        await firstPage.bringToFront();
        log.success('Successfully brought page to front');
        
        // Test screenshot capability
        const screenshotPath = './screenshots/test-screenshot.png';
        await firstPage.screenshot({ 
          path: screenshotPath,
          fullPage: false 
        });
        log.success(`Screenshot saved: ${screenshotPath}`);
        
        // Test JavaScript execution
        const result = await firstPage.evaluate(() => {
          return {
            title: document.title,
            url: window.location.href,
            userAgent: navigator.userAgent,
            viewport: {
              width: window.innerWidth,
              height: window.innerHeight
            }
          };
        });
        
        log.success('JavaScript execution successful');
        log.info('Page info:', JSON.stringify(result, null, 2));
        
      } catch (error) {
        log.error('Page interaction failed:', error.message);
      }
    }
    
    // Test 5: Disconnect (but leave Chrome running)
    log.info('Step 5: Disconnecting from Chrome...');
    await browser.disconnect();
    log.success('Successfully disconnected (Chrome remains running)');
    
    log.success('\n=== ALL TESTS PASSED ===');
    log.info('Your Chrome browser automation setup is working correctly!');
    
  } catch (error) {
    reportError(error, { test: 'Chrome connection test' });
    
    // Provide helpful error messages
    if (error.type === ErrorTypes.CONNECTION_FAILED) {
      log.error('\nTroubleshooting steps:');
      log.error('1. Make sure Chrome is running with remote debugging enabled:');
      log.error(`   chrome --remote-debugging-port=${config.chrome.debuggingPort}`);
      log.error('2. Check if another process is using the debugging port:');
      log.error(`   lsof -i :${config.chrome.debuggingPort}`);
      log.error('3. Try restarting Chrome with debugging enabled');
    }
    
    process.exit(1);
  }
}

async function testRetryLogic() {
  log.info('\nTesting retry logic...');
  
  let attempts = 0;
  const maxAttempts = 3;
  
  try {
    await retryWithBackoff(async () => {
      attempts++;
      log.info(`Retry test attempt ${attempts}`);
      
      if (attempts < maxAttempts) {
        throw new Error(`Simulated failure ${attempts}`);
      }
      
      return 'Success!';
    }, maxAttempts, 500);
    
    log.success('Retry logic test passed');
    
  } catch (error) {
    log.error('Retry logic test failed:', error.message);
  }
}

async function runAllTests() {
  log.info('Starting browser automation tests...\n');
  
  try {
    await testChromeConnection();
    await testRetryLogic();
    
    log.success('\n🎉 All tests completed successfully!');
    log.info('You can now use the automation tool with confidence.');
    
  } catch (error) {
    log.error('\n❌ Tests failed');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests();
}

export { testChromeConnection, testRetryLogic, runAllTests };
