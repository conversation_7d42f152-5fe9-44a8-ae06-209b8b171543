/**
 * Error handling utilities for browser automation
 */

import chalk from 'chalk';

// Error types
export const ErrorTypes = {
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  CHROME_NOT_FOUND: 'CHROME_NOT_FOUND',
  TAB_NOT_FOUND: 'TAB_NOT_FOUND',
  TASK_FAILED: 'TASK_FAILED',
  TIMEOUT: 'TIMEOUT',
  NAVIGATION_FAILED: 'NAVIGATION_FAILED',
  ELEMENT_NOT_FOUND: 'ELEMENT_NOT_FOUND'
};

// Custom error classes
export class AutomationError extends Error {
  constructor(message, type = 'UNKNOWN', details = {}) {
    super(message);
    this.name = 'AutomationError';
    this.type = type;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

export class ConnectionError extends AutomationError {
  constructor(message, details = {}) {
    super(message, ErrorTypes.CONNECTION_FAILED, details);
    this.name = 'ConnectionError';
  }
}

export class TaskError extends AutomationError {
  constructor(message, taskName, details = {}) {
    super(message, ErrorTypes.TASK_FAILED, { taskName, ...details });
    this.name = 'TaskError';
  }
}

// Retry utility with exponential backoff
export async function retryWithBackoff(
  operation,
  maxRetries = 3,
  baseDelay = 1000,
  maxDelay = 10000,
  backoffFactor = 2
) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }
      
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
      console.log(chalk.yellow(`[RETRY] Attempt ${attempt} failed, retrying in ${delay}ms...`));
      console.log(chalk.gray(`[RETRY] Error: ${error.message}`));
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new AutomationError(
    `Operation failed after ${maxRetries} attempts: ${lastError.message}`,
    'RETRY_EXHAUSTED',
    { originalError: lastError, attempts: maxRetries }
  );
}

// Timeout wrapper
export function withTimeout(promise, timeoutMs, errorMessage = 'Operation timed out') {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new AutomationError(errorMessage, ErrorTypes.TIMEOUT, { timeoutMs }));
      }, timeoutMs);
    })
  ]);
}

// Safe element interaction with retries
export async function safeElementInteraction(page, selector, action, options = {}) {
  const {
    timeout = 5000,
    retries = 3,
    waitForVisible = true,
    waitForEnabled = true
  } = options;
  
  return retryWithBackoff(async () => {
    // Wait for element to exist
    await page.waitForSelector(selector, { timeout });
    
    const element = page.locator(selector).first();
    
    // Wait for element to be visible if required
    if (waitForVisible) {
      await element.waitFor({ state: 'visible', timeout });
    }
    
    // Wait for element to be enabled if required
    if (waitForEnabled && ['click', 'fill', 'type'].includes(action)) {
      await element.waitFor({ state: 'attached', timeout });
      
      // Check if element is enabled
      const isEnabled = await element.isEnabled();
      if (!isEnabled) {
        throw new AutomationError(
          `Element is not enabled: ${selector}`,
          ErrorTypes.ELEMENT_NOT_FOUND
        );
      }
    }
    
    // Perform the action
    switch (action) {
      case 'click':
        await element.click();
        break;
      case 'fill':
        await element.fill(options.value || '');
        break;
      case 'type':
        await element.type(options.value || '', { delay: options.delay || 100 });
        break;
      case 'getText':
        return await element.textContent();
      case 'getAttribute':
        return await element.getAttribute(options.attribute);
      default:
        throw new AutomationError(`Unknown action: ${action}`, ErrorTypes.TASK_FAILED);
    }
    
  }, retries, 1000);
}

// Safe navigation with error handling
export async function safeNavigate(page, url, options = {}) {
  const {
    timeout = 30000,
    waitUntil = 'networkidle',
    retries = 2
  } = options;
  
  return retryWithBackoff(async () => {
    try {
      await withTimeout(
        page.goto(url, { waitUntil, timeout }),
        timeout,
        `Navigation to ${url} timed out after ${timeout}ms`
      );
      
      // Verify we're on the expected page
      const currentUrl = page.url();
      if (!currentUrl.includes(new URL(url).hostname)) {
        throw new AutomationError(
          `Navigation failed: expected ${url}, got ${currentUrl}`,
          ErrorTypes.NAVIGATION_FAILED
        );
      }
      
    } catch (error) {
      if (error.name === 'TimeoutError' || error.type === ErrorTypes.TIMEOUT) {
        throw new AutomationError(
          `Navigation to ${url} timed out`,
          ErrorTypes.NAVIGATION_FAILED,
          { url, timeout }
        );
      }
      throw error;
    }
  }, retries, 2000);
}

// Error reporter
export function reportError(error, context = {}) {
  console.error(chalk.red('\n=== AUTOMATION ERROR ==='));
  console.error(chalk.red(`Type: ${error.type || 'UNKNOWN'}`));
  console.error(chalk.red(`Message: ${error.message}`));
  
  if (error.details) {
    console.error(chalk.yellow('Details:'), JSON.stringify(error.details, null, 2));
  }
  
  if (context.page) {
    console.error(chalk.yellow('Page URL:'), context.page.url());
  }
  
  if (error.stack) {
    console.error(chalk.gray('Stack trace:'));
    console.error(chalk.gray(error.stack));
  }
  
  console.error(chalk.red('========================\n'));
}

// Recovery strategies
export const RecoveryStrategies = {
  // Reload page and retry
  async reloadAndRetry(page, operation) {
    console.log(chalk.yellow('[RECOVERY] Reloading page and retrying...'));
    await page.reload({ waitUntil: 'networkidle' });
    return await operation();
  },
  
  // Go back and retry
  async goBackAndRetry(page, operation) {
    console.log(chalk.yellow('[RECOVERY] Going back and retrying...'));
    await page.goBack({ waitUntil: 'networkidle' });
    return await operation();
  },
  
  // Clear cookies and retry
  async clearCookiesAndRetry(page, operation) {
    console.log(chalk.yellow('[RECOVERY] Clearing cookies and retrying...'));
    await page.context().clearCookies();
    return await operation();
  }
};

// Graceful shutdown handler
export function setupGracefulShutdown(browser) {
  const cleanup = async () => {
    console.log(chalk.yellow('\n[SHUTDOWN] Gracefully disconnecting from browser...'));
    try {
      if (browser) {
        await browser.disconnect();
        console.log(chalk.green('[SHUTDOWN] Successfully disconnected'));
      }
    } catch (error) {
      console.error(chalk.red('[SHUTDOWN] Error during cleanup:'), error.message);
    }
    process.exit(0);
  };
  
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('uncaughtException', (error) => {
    console.error(chalk.red('[FATAL] Uncaught exception:'), error);
    cleanup();
  });
}

export default {
  ErrorTypes,
  AutomationError,
  ConnectionError,
  TaskError,
  retryWithBackoff,
  withTimeout,
  safeElementInteraction,
  safeNavigate,
  reportError,
  RecoveryStrategies,
  setupGracefulShutdown
};
