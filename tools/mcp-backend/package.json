{"name": "mcp-backend", "version": "1.0.0", "description": "MCP server backend with WebSocket support for browser extension", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "dev:backend": "tsx watch src/backend-server.ts", "build": "tsc", "start": "node dist/index.js", "start:backend": "node dist/backend-server.js", "backend": "npm run build && npm run start:backend"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "express": "^4.18.2", "ws": "^8.16.0", "cors": "^2.8.5", "uuid": "^9.0.1", "node-fetch": "^3.3.2", "jsdom": "^23.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/ws": "^8.5.10", "@types/cors": "^2.8.17", "@types/node": "^20.11.0", "@types/uuid": "^9.0.7", "@types/jsdom": "^21.1.6", "typescript": "^5.3.3", "tsx": "^4.7.0"}}