// Browser automation helper functions that can be executed via browser_execute

export const automationHelpers = {
  // Analyze current page and return structured data
  analyzePage: `
    function analyzePage() {
      const analysis = {
        pageInfo: {
          title: document.title,
          url: window.location.href,
          domain: window.location.hostname
        },
        forms: [],
        links: [],
        buttons: [],
        inputs: []
      };

      // Find all forms
      document.querySelectorAll('form').forEach((form, index) => {
        const formInfo = {
          index: index,
          id: form.id,
          action: form.action,
          method: form.method,
          inputs: []
        };
        
        form.querySelectorAll('input, textarea, select').forEach(input => {
          if (input.type !== 'hidden') {
            formInfo.inputs.push({
              type: input.tagName.toLowerCase(),
              inputType: input.type,
              name: input.name,
              id: input.id,
              placeholder: input.placeholder,
              value: input.value,
              selector: input.id ? '#' + input.id : null
            });
          }
        });
        
        analysis.forms.push(formInfo);
      });

      // Find all clickable elements
      document.querySelectorAll('a').forEach(link => {
        if (link.href && link.offsetParent !== null) {
          analysis.links.push({
            text: link.textContent.trim(),
            href: link.href,
            selector: link.id ? '#' + link.id : null
          });
        }
      });

      document.querySelectorAll('button, [type="submit"], [type="button"]').forEach(button => {
        if (button.offsetParent !== null) {
          analysis.buttons.push({
            text: button.textContent.trim(),
            type: button.type,
            selector: button.id ? '#' + button.id : null
          });
        }
      });

      // Find all visible inputs
      document.querySelectorAll('input, textarea').forEach(input => {
        if (input.type !== 'hidden' && input.offsetParent !== null) {
          analysis.inputs.push({
            type: input.type,
            name: input.name,
            id: input.id,
            placeholder: input.placeholder,
            value: input.value,
            selector: input.id ? '#' + input.id : null
          });
        }
      });

      return analysis;
    }
    analyzePage();
  `,

  // Find element by text content
  findByText: (text: string) => `
    function findByText(searchText) {
      const elements = Array.from(document.querySelectorAll('*'));
      const matches = [];
      
      elements.forEach(el => {
        if (el.textContent && el.textContent.trim() === searchText.trim()) {
          // Check if it's a leaf node (no child elements with text)
          const hasTextChildren = Array.from(el.children).some(
            child => child.textContent && child.textContent.trim()
          );
          
          if (!hasTextChildren) {
            matches.push({
              tag: el.tagName.toLowerCase(),
              text: el.textContent.trim(),
              selector: el.id ? '#' + el.id : null,
              className: el.className
            });
          }
        }
      });
      
      return matches;
    }
    findByText("${text}");
  `,

  // Click element by selector
  clickElement: (selector: string) => `
    function clickElement(selector) {
      const element = document.querySelector("${selector}");
      if (element) {
        element.click();
        return { success: true, message: "Clicked element: ${selector}" };
      }
      return { success: false, message: "Element not found: ${selector}" };
    }
    clickElement("${selector}");
  `,

  // Fill input field
  fillInput: (selector: string, value: string) => `
    function fillInput(selector, value) {
      const input = document.querySelector("${selector}");
      if (input) {
        input.focus();
        input.value = "${value}";
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        return { success: true, message: "Filled input: ${selector}" };
      }
      return { success: false, message: "Input not found: ${selector}" };
    }
    fillInput("${selector}", "${value}");
  `,

  // Submit form
  submitForm: (formSelector: string) => `
    function submitForm(selector) {
      const form = document.querySelector("${formSelector}");
      if (form) {
        form.submit();
        return { success: true, message: "Submitted form: ${formSelector}" };
      }
      return { success: false, message: "Form not found: ${formSelector}" };
    }
    submitForm("${formSelector}");
  `,

  // Wait for element
  waitForElement: (selector: string, timeout: number = 5000) => `
    function waitForElement(selector, timeout) {
      return new Promise((resolve) => {
        const startTime = Date.now();
        
        const checkElement = () => {
          const element = document.querySelector(selector);
          if (element) {
            resolve({ success: true, message: "Element found: " + selector });
          } else if (Date.now() - startTime > timeout) {
            resolve({ success: false, message: "Timeout waiting for: " + selector });
          } else {
            setTimeout(checkElement, 100);
          }
        };
        
        checkElement();
      });
    }
    waitForElement("${selector}", ${timeout});
  `,

  // Login helper
  loginHelper: (usernameSelector: string, passwordSelector: string, username: string, password: string) => `
    async function login() {
      const results = [];
      
      // Fill username
      const usernameInput = document.querySelector("${usernameSelector}");
      if (usernameInput) {
        usernameInput.focus();
        usernameInput.value = "${username}";
        usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
        results.push({ step: "username", success: true });
      } else {
        results.push({ step: "username", success: false, error: "Username field not found" });
        return results;
      }
      
      // Fill password
      const passwordInput = document.querySelector("${passwordSelector}");
      if (passwordInput) {
        passwordInput.focus();
        passwordInput.value = "${password}";
        passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
        results.push({ step: "password", success: true });
      } else {
        results.push({ step: "password", success: false, error: "Password field not found" });
        return results;
      }
      
      // Find and click submit button
      const form = usernameInput.closest('form') || passwordInput.closest('form');
      if (form) {
        const submitBtn = form.querySelector('[type="submit"], button');
        if (submitBtn) {
          submitBtn.click();
          results.push({ step: "submit", success: true });
        } else {
          form.submit();
          results.push({ step: "submit", success: true, method: "form.submit()" });
        }
      } else {
        results.push({ step: "submit", success: false, error: "Form not found" });
      }
      
      return results;
    }
    login();
  `
};