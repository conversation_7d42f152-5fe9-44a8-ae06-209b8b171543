<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Test Pages - MCP Browser Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .policy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .policy-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        .policy-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .policy-card h3 {
            margin-top: 0;
            color: #2563eb;
        }
        .policy-card p {
            color: #666;
            margin: 10px 0;
        }
        .policy-card code {
            display: block;
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .policy-card a {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .policy-card a:hover {
            background: #1d4ed8;
        }
        .strict { border-color: #ef4444; }
        .linkedin { border-color: #0077b5; }
        .moderate { border-color: #f59e0b; }
        .permissive { border-color: #10b981; }
        .none { border-color: #6b7280; }
        .info-box {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .extension-status {
            margin-top: 20px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
        #status {
            font-weight: 600;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSP Test Pages</h1>
        <p>Test the MCP Browser Extension against various Content Security Policy (CSP) configurations</p>
        
        <div class="info-box">
            <strong>About CSP Testing:</strong>
            <p>These pages simulate real-world CSP restrictions found on production websites like LinkedIn and staging.research-triangle.ai. 
            Use them to test if the browser extension can successfully execute JavaScript patterns without triggering CSP violations.</p>
        </div>

        <div class="extension-status">
            <strong>Extension Status:</strong> <span id="status">Checking...</span>
        </div>

        <div class="policy-grid">
            <div class="policy-card strict">
                <h3>Strict Policy</h3>
                <p>Most restrictive - Similar to staging.research-triangle.ai</p>
                <code>script-src 'self'; no eval, no inline scripts</code>
                <p>Tests pattern matching without any dynamic code execution</p>
                <a href="/test/csp/strict">Open Test Page</a>
            </div>

            <div class="policy-card linkedin">
                <h3>LinkedIn Policy</h3>
                <p>Production LinkedIn-like CSP configuration</p>
                <code>script-src 'self' 'unsafe-inline' https://static.licdn.com</code>
                <p>Allows some inline scripts but blocks eval</p>
                <a href="/test/csp/linkedin">Open Test Page</a>
            </div>

            <div class="policy-card moderate">
                <h3>Moderate Policy</h3>
                <p>Common configuration for many web apps</p>
                <code>script-src 'self' 'unsafe-inline'</code>
                <p>Allows inline scripts but still blocks eval</p>
                <a href="/test/csp/moderate">Open Test Page</a>
            </div>

            <div class="policy-card permissive">
                <h3>Permissive Policy</h3>
                <p>Allows eval - For comparison testing</p>
                <code>script-src 'self' 'unsafe-inline' 'unsafe-eval'</code>
                <p>Most permissive - shows what works with eval</p>
                <a href="/test/csp/permissive">Open Test Page</a>
            </div>

            <div class="policy-card none">
                <h3>No CSP</h3>
                <p>Baseline - No security policy headers</p>
                <code>No Content-Security-Policy header</code>
                <p>Use as baseline to compare functionality</p>
                <a href="/test/csp/none">Open Test Page</a>
            </div>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f9fafb; border-radius: 4px;">
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Make sure the MCP Browser Extension is installed and connected</li>
                <li>Open one of the test pages above</li>
                <li>Use the extension's tools to interact with the page:
                    <ul>
                        <li><code>document.title</code> - Get page title</li>
                        <li><code>click("#test-alert")</code> - Click the alert button</li>
                        <li><code>type("#title", "Test text")</code> - Type in form fields</li>
                        <li><code>document.querySelector("#title").value</code> - Get field values</li>
                    </ul>
                </li>
                <li>Check browser console for any CSP violations</li>
            </ol>
        </div>
    </div>

    <script>
        // Check extension connection status
        fetch('http://localhost:3636/api/status')
            .then(res => res.json())
            .then(data => {
                const statusEl = document.getElementById('status');
                if (data.connectedClients > 0) {
                    statusEl.textContent = `Connected (${data.connectedClients} client${data.connectedClients > 1 ? 's' : ''})`;
                    statusEl.style.color = '#10b981';
                } else {
                    statusEl.textContent = 'Not connected - Please ensure the extension is active';
                    statusEl.style.color = '#ef4444';
                }
            })
            .catch(err => {
                document.getElementById('status').textContent = 'Backend server not reachable';
                document.getElementById('status').style.color = '#ef4444';
            });
    </script>
</body>
</html>