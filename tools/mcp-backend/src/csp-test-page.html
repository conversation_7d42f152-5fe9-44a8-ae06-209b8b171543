<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Test Page - MCP Browser Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .csp-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        input[type="text"],
        input[type="email"],
        textarea,
        select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-section {
            border-top: 1px solid #eee;
            margin-top: 30px;
            padding-top: 20px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            display: block;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            display: block;
        }
        .element-list {
            list-style: none;
            padding: 0;
        }
        .element-list li {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 4px;
            cursor: pointer;
        }
        .element-list li:hover {
            background: #e9ecef;
        }
        #dynamic-content {
            min-height: 100px;
            border: 1px dashed #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSP Test Page</h1>
        <p>This page enforces strict Content Security Policy (CSP) similar to LinkedIn and staging.research-triangle.ai</p>
        
        <div class="csp-info">
            <strong>Active CSP Policy:</strong>
            <code>default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self';</code>
            <br><br>
            <em>This policy blocks all inline scripts, eval(), and Function() constructor - just like production sites!</em>
        </div>

        <div class="test-section">
            <h2>Test Form</h2>
            <form id="test-form">
                <div class="form-group">
                    <label for="title">Title *</label>
                    <input type="text" id="title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="4"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="category">Category</label>
                    <select id="category" name="category">
                        <option value="">Select a category</option>
                        <option value="news">News</option>
                        <option value="blog">Blog Post</option>
                        <option value="tutorial">Tutorial</option>
                        <option value="announcement">Announcement</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="email">Author Email</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="published" name="published">
                        Publish immediately
                    </label>
                </div>
                
                <button type="submit">Submit</button>
                <button type="button" id="clear-btn">Clear Form</button>
            </form>
            
            <div id="form-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>Interactive Elements</h2>
            <p>Test various UI interactions:</p>
            
            <button id="test-alert">Show Alert</button>
            <button id="test-confirm">Show Confirm</button>
            <button id="add-element">Add Dynamic Element</button>
            <button id="toggle-visibility">Toggle Content</button>
            
            <div id="dynamic-content">
                <p id="toggle-target">This content can be toggled on/off</p>
            </div>
            
            <h3>Clickable List Items</h3>
            <ul class="element-list">
                <li data-value="item1">Click me - Item 1</li>
                <li data-value="item2">Click me - Item 2</li>
                <li data-value="item3">Click me - Item 3</li>
            </ul>
            
            <div id="interaction-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>Navigation Links</h2>
            <p>Test navigation and link clicking:</p>
            <nav>
                <a href="#section1" class="nav-link">Section 1</a> |
                <a href="#section2" class="nav-link">Section 2</a> |
                <a href="#section3" class="nav-link">Section 3</a> |
                <a href="https://example.com" class="external-link" target="_blank">External Link</a>
            </nav>
        </div>
    </div>

    <script>
        // Safe inline script that doesn't use eval or Function constructor
        document.addEventListener('DOMContentLoaded', function() {
            // Form submission
            document.getElementById('test-form').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);
                showResult('form-result', 'Form submitted with data: ' + JSON.stringify(data), 'success');
            });

            // Clear form
            document.getElementById('clear-btn').addEventListener('click', function() {
                document.getElementById('test-form').reset();
                showResult('form-result', 'Form cleared', 'success');
            });

            // Test alert
            document.getElementById('test-alert').addEventListener('click', function() {
                alert('This is a test alert from CSP-restricted page!');
                showResult('interaction-result', 'Alert shown', 'success');
            });

            // Test confirm
            document.getElementById('test-confirm').addEventListener('click', function() {
                const result = confirm('This is a test confirm dialog. Click OK or Cancel.');
                showResult('interaction-result', 'Confirm result: ' + result, 'success');
            });

            // Add dynamic element
            document.getElementById('add-element').addEventListener('click', function() {
                const elem = document.createElement('div');
                elem.textContent = 'Dynamic element added at ' + new Date().toLocaleTimeString();
                elem.style.padding = '10px';
                elem.style.margin = '5px 0';
                elem.style.background = '#e3f2fd';
                elem.style.borderRadius = '4px';
                document.getElementById('dynamic-content').appendChild(elem);
                showResult('interaction-result', 'Dynamic element added', 'success');
            });

            // Toggle visibility
            document.getElementById('toggle-visibility').addEventListener('click', function() {
                const target = document.getElementById('toggle-target');
                target.style.display = target.style.display === 'none' ? 'block' : 'none';
                showResult('interaction-result', 'Content toggled', 'success');
            });

            // List item clicks
            document.querySelectorAll('.element-list li').forEach(function(li) {
                li.addEventListener('click', function() {
                    showResult('interaction-result', 'Clicked: ' + this.textContent + ' (value: ' + this.dataset.value + ')', 'success');
                });
            });

            // Navigation links
            document.querySelectorAll('.nav-link').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    showResult('interaction-result', 'Navigation to: ' + this.getAttribute('href'), 'success');
                });
            });

            function showResult(elementId, message, type) {
                const elem = document.getElementById(elementId);
                elem.textContent = message;
                elem.className = 'result ' + type;
                setTimeout(function() {
                    elem.style.display = 'none';
                }, 5000);
            }
        });
    </script>
</body>
</html>