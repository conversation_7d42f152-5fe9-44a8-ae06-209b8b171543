import { J<PERSON><PERSON> } from 'jsdom';

interface PageElement {
  selector: string;
  type: string;
  text: string;
  attributes: Record<string, string>;
  position?: { x: number; y: number; width: number; height: number };
  context: {
    label?: string;
    nearbyText?: string;
    formId?: string;
    parentSection?: string;
  };
}

interface PageAnalysis {
  pageInfo: {
    title: string;
    url: string;
    isLoggedIn: boolean;
    currentSection?: string;
  };
  navigation: {
    mainMenu: Array<{ text: string; url: string; selector: string }>;
    userMenu: Array<{ text: string; url: string; selector: string }>;
    adminToolbar?: Array<{ text: string; id: string; selector: string }>;
  };
  forms: Array<{
    id: string;
    action: string;
    fields: Array<{
      type: string;
      name: string;
      selector: string;
      label?: string;
    }>;
    submitButton?: { text: string; selector: string };
  }>;
  interactiveElements: PageElement[];
}

export class PageAnalyzer {
  private dom: JSDOM;
  private document: Document;

  constructor(htmlContent: string, url: string) {
    this.dom = new JSDOM(htmlContent, { url });
    this.document = this.dom.window.document;
  }

  analyze(): PageAnalysis {
    return {
      pageInfo: this.analyzePageInfo(),
      navigation: this.analyzeNavigation(),
      forms: this.analyzeForms(),
      interactiveElements: this.analyzeInteractiveElements(),
    };
  }

  private analyzePageInfo() {
    return {
      title: this.document.title,
      url: this.dom.window.location.href,
      isLoggedIn: this.document.body.innerHTML.includes('Log out'),
      currentSection: this.document.querySelector('.breadcrumb-title')?.textContent || undefined,
    };
  }

  private analyzeNavigation() {
    const navigation = {
      mainMenu: [] as Array<{ text: string; url: string; selector: string }>,
      userMenu: [] as Array<{ text: string; url: string; selector: string }>,
      adminToolbar: [] as Array<{ text: string; id: string; selector: string }>,
    };

    // Main menu
    this.document.querySelectorAll('.navigation__menubar-main > li > a').forEach((link, index) => {
      const anchor = link as HTMLAnchorElement;
      navigation.mainMenu.push({
        text: anchor.textContent?.trim() || '',
        url: anchor.href,
        selector: `.navigation__menubar-main > li:nth-child(${index + 1}) > a`,
      });
    });

    // User menu
    this.document.querySelectorAll('.navigation__menubar-account a').forEach((link, index) => {
      const anchor = link as HTMLAnchorElement;
      navigation.userMenu.push({
        text: anchor.textContent?.trim() || '',
        url: anchor.href,
        selector: `.navigation__menubar-account a:nth-child(${index + 1})`,
      });
    });

    // Admin toolbar (if present)
    this.document.querySelectorAll('#toolbar-bar .toolbar-tab a.trigger').forEach((trigger) => {
      const anchor = trigger as HTMLAnchorElement;
      if (anchor.id) {
        navigation.adminToolbar?.push({
          text: anchor.textContent?.trim() || '',
          id: anchor.id,
          selector: `#${anchor.id}`,
        });
      }
    });

    return navigation;
  }

  private analyzeForms() {
    const forms: PageAnalysis['forms'] = [];

    this.document.querySelectorAll('form').forEach((form, formIndex) => {
      const formElement = form as HTMLFormElement;
      const formData = {
        id: formElement.id || `form-${formIndex}`,
        action: formElement.action,
        fields: [] as Array<{ type: string; name: string; selector: string; label?: string }>,
        submitButton: undefined as { text: string; selector: string } | undefined,
      };

      // Analyze form fields
      formElement.querySelectorAll('input, textarea, select').forEach((field) => {
        const input = field as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
        if (input.type !== 'hidden') {
          formData.fields.push({
            type: input.tagName.toLowerCase(),
            name: input.name,
            selector: this.generateSelector(input),
            label: this.findLabelForField(input),
          });
        }
      });

      // Find submit button
      const submitButton = formElement.querySelector('[type="submit"], button');
      if (submitButton) {
        formData.submitButton = {
          text: submitButton.textContent?.trim() || '',
          selector: this.generateSelector(submitButton),
        };
      }

      if (formData.fields.length > 0) {
        forms.push(formData);
      }
    });

    return forms;
  }

  private analyzeInteractiveElements(): PageElement[] {
    const elements: PageElement[] = [];
    const selectors = 'input, button, textarea, select, a, [role="button"], [onclick]';

    this.document.querySelectorAll(selectors).forEach((element) => {
      const el = element as HTMLElement;
      
      // Skip hidden elements
      if (el.style.display === 'none' || el.style.visibility === 'hidden') {
        return;
      }

      const elementInfo: PageElement = {
        selector: this.generateSelector(el),
        type: el.tagName.toLowerCase(),
        text: this.getElementText(el),
        attributes: this.getElementAttributes(el),
        context: this.getElementContext(el),
      };

      elements.push(elementInfo);
    });

    return elements;
  }

  private generateSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }

    const path: string[] = [];
    let current: Element | null = element;

    while (current && current.nodeType === 1) {
      let selector = current.nodeName.toLowerCase();

      if (current.id) {
        selector = `#${current.id}`;
        path.unshift(selector);
        break;
      }

      if (current.className && typeof current.className === 'string') {
        const classes = current.className.split(' ').filter(c => c && !c.includes(' '));
        if (classes.length > 0) {
          selector += `.${classes[0]}`;
        }
      }

      const parent = current.parentNode;
      if (parent) {
        const siblings = Array.from(parent.children);
        const sameTagSiblings = siblings.filter(s => s.nodeName === current!.nodeName);
        
        if (sameTagSiblings.length > 1) {
          const index = sameTagSiblings.indexOf(current) + 1;
          selector += `:nth-of-type(${index})`;
        }
      }

      path.unshift(selector);
      current = current.parentElement;
    }

    return path.join(' > ');
  }

  private findLabelForField(field: HTMLElement): string {
    // Check for label with for attribute
    if (field.id) {
      const label = this.document.querySelector(`label[for="${field.id}"]`);
      if (label) return label.textContent?.trim() || '';
    }

    // Check for aria-label
    const ariaLabel = field.getAttribute('aria-label');
    if (ariaLabel) return ariaLabel;

    // Check for placeholder
    if ('placeholder' in field) {
      const placeholder = (field as HTMLInputElement).placeholder;
      if (placeholder) return placeholder;
    }

    // Check for title
    if (field.title) return field.title;

    return '';
  }

  private getElementText(element: HTMLElement): string {
    if ('value' in element) {
      return (element as HTMLInputElement).value || '';
    }
    return element.textContent?.trim() || 
           element.getAttribute('aria-label') || 
           element.title || 
           '';
  }

  private getElementAttributes(element: HTMLElement): Record<string, string> {
    const attrs: Record<string, string> = {};
    
    ['id', 'class', 'name', 'type', 'href', 'aria-label', 'role', 'placeholder'].forEach(attr => {
      const value = element.getAttribute(attr);
      if (value) {
        attrs[attr] = value;
      }
    });

    return attrs;
  }

  private getElementContext(element: HTMLElement): PageElement['context'] {
    const context: PageElement['context'] = {};

    // Find parent form
    const form = element.closest('form');
    if (form) {
      context.formId = form.id || undefined;
    }

    // Find parent section
    const section = element.closest('section, article, nav, header, footer, [role]');
    if (section) {
      context.parentSection = section.getAttribute('aria-label') || 
                            section.id || 
                            section.className || 
                            undefined;
    }

    // Get label
    context.label = this.findLabelForField(element);

    return context;
  }

  // Method to find elements by description (for LLM use)
  findElementByDescription(description: string): PageElement | null {
    const lowerDesc = description.toLowerCase();
    const elements = this.analyzeInteractiveElements();

    // Try exact text match first
    let found = elements.find(el => el.text.toLowerCase() === lowerDesc);
    if (found) return found;

    // Try partial text match
    found = elements.find(el => el.text.toLowerCase().includes(lowerDesc));
    if (found) return found;

    // Try label match
    found = elements.find(el => el.context.label?.toLowerCase().includes(lowerDesc));
    if (found) return found;

    // Try attribute match
    found = elements.find(el => 
      Object.values(el.attributes).some(val => val.toLowerCase().includes(lowerDesc))
    );
    if (found) return found;

    return null;
  }

  // Generate action plan for common tasks
  generateActionPlan(task: string): Array<{ action: string; selector: string; value?: string }> {
    const plan: Array<{ action: string; selector: string; value?: string }> = [];
    const taskLower = task.toLowerCase();

    if (taskLower.includes('search')) {
      const searchForm = this.document.querySelector('#search-block-form');
      if (searchForm) {
        const searchInput = searchForm.querySelector('input[type="search"]');
        const submitButton = searchForm.querySelector('[type="submit"]');
        
        if (searchInput) {
          plan.push({
            action: 'click',
            selector: this.generateSelector(searchInput),
          });
          plan.push({
            action: 'type',
            selector: this.generateSelector(searchInput),
            value: 'search term',
          });
        }
        
        if (submitButton) {
          plan.push({
            action: 'click',
            selector: this.generateSelector(submitButton),
          });
        }
      }
    }

    if (taskLower.includes('log out') || taskLower.includes('logout')) {
      const logoutLink = Array.from(this.document.querySelectorAll('a')).find(
        a => a.textContent?.toLowerCase().includes('log out')
      );
      
      if (logoutLink) {
        plan.push({
          action: 'click',
          selector: this.generateSelector(logoutLink),
        });
      }
    }

    return plan;
  }
}