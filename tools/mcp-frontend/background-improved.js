// Improved execute handler using chrome.scripting API for Manifest V3

// <PERSON>le execute request - IMPROVED VERSION
async function handleExecuteV3(requestId, data) {
  log('INFO', 'Handling execute request with chrome.scripting API', { requestId, url: data.url });

  try {
    // Find or create appropriate tab
    const tab = await findOrCreateTab(data.url);
    
    // Wait a bit for navigation if URL was provided
    if (data.url) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Use chrome.scripting.executeScript for dynamic code execution
    // This bypasses CSP restrictions
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: executeInPage,
      args: [data.code]
    });

    if (results && results[0]) {
      const result = results[0].result;
      
      if (result.error) {
        sendToBackend({
          type: 'request_failed',
          requestId,
          error: result.error
        });
      } else {
        sendToBackend({
          type: 'request_complete',
          requestId,
          result: { result }
        });
      }
    } else {
      throw new Error('No result from script execution');
    }
  } catch (error) {
    log('ERROR', 'Script execution failed', { error: error.message });
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Function to be injected and executed in the page context
function executeInPage(code) {
  try {
    // Helper functions available in the executed code
    const helpers = {
      // DOM utilities
      querySelector: (sel) => document.querySelector(sel),
      querySelectorAll: (sel) => document.querySelectorAll(sel),
      
      // Wait function
      sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
      
      // Click helper
      click: (selector) => {
        const el = document.querySelector(selector);
        if (el) {
          el.click();
          return true;
        }
        return false;
      },
      
      // Type helper
      type: (selector, text) => {
        const el = document.querySelector(selector);
        if (el) {
          el.focus();
          el.value = text;
          el.dispatchEvent(new Event('input', { bubbles: true }));
          el.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }
        return false;
      },
      
      // Wait for element
      waitForElement: (selector, timeout = 10000) => {
        return new Promise((resolve, reject) => {
          const startTime = Date.now();
          
          const check = () => {
            const el = document.querySelector(selector);
            if (el) {
              resolve(el);
            } else if (Date.now() - startTime > timeout) {
              reject(new Error(`Element ${selector} not found after ${timeout}ms`));
            } else {
              setTimeout(check, 100);
            }
          };
          
          check();
        });
      }
    };
    
    // Create function with helpers in scope
    const AsyncFunction = (async function() {}).constructor;
    const fn = new AsyncFunction(...Object.keys(helpers), 'document', 'window', code);
    
    // Execute and return result
    return fn(...Object.values(helpers), document, window);
  } catch (error) {
    return { error: error.message };
  }
}

// Alternative: Use declarativeContent or activeTab permissions
async function handleExecuteSimple(requestId, data) {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    // For simple operations, we can use predefined functions
    const operations = {
      getTitle: async () => {
        const results = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: () => document.title
        });
        return results[0].result;
      },
      
      getUrl: async () => {
        const results = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: () => window.location.href
        });
        return results[0].result;
      },
      
      countElements: async (selector) => {
        const results = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: (sel) => document.querySelectorAll(sel).length,
          args: [selector]
        });
        return results[0].result;
      },
      
      clickElement: async (selector) => {
        const results = await chrome.scripting.executeScript({
          target: { tabId: tab.id },
          func: (sel) => {
            const el = document.querySelector(sel);
            if (el) {
              el.click();
              return { clicked: true, selector: sel };
            }
            return { clicked: false, error: 'Element not found' };
          },
          args: [selector]
        });
        return results[0].result;
      }
    };
    
    // Parse the code to determine operation
    const code = data.code.trim();
    
    if (code === 'document.title') {
      const result = await operations.getTitle();
      sendToBackend({
        type: 'request_complete',
        requestId,
        result: { result: { value: result } }
      });
    } else if (code.includes('click(')) {
      const match = code.match(/click\(['"]([^'"]+)['"]\)/);
      if (match) {
        const result = await operations.clickElement(match[1]);
        sendToBackend({
          type: 'request_complete',
          requestId,
          result: { result: { value: result } }
        });
      }
    } else {
      // Fall back to full execution
      await handleExecuteV3(requestId, data);
    }
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}