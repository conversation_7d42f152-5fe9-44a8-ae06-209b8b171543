// Background service worker for flexible browser automation

// Global variables
self.ws = null;
self.reconnectInterval = null;
const WS_URL = 'ws://localhost:3636';

// Logger utility
const log = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [BACKGROUND] [${level}] ${message}`;
  if (data) {
    console.log(logMessage, data);
  } else {
    console.log(logMessage);
  }
};

// WebSocket connection management
function connectWebSocket() {
  if (self.ws && self.ws.readyState === WebSocket.OPEN) {
    log('DEBUG', 'WebSocket already connected');
    return;
  }

  if (self.ws) {
    self.ws.close();
    self.ws = null;
  }

  try {
    log('INFO', 'Attempting to connect to MCP backend', { url: WS_URL });
    self.ws = new WebSocket(WS_URL);

    self.ws.onopen = () => {
      log('INFO', 'Connected to MCP backend successfully');
      if (self.reconnectInterval) {
        clearInterval(self.reconnectInterval);
        self.reconnectInterval = null;
      }

      // Notify all tabs that we're connected
      chrome.tabs.query({}, (tabs) => {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            action: 'backend_connected'
          }).catch(() => {});
        });
      });
    };

    self.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      log('INFO', 'Received message from backend', message);

      // Route message based on type
      handleBackendMessage(message);
    };

    self.ws.onerror = (error) => {
      log('ERROR', 'WebSocket error', { error: error.message || 'Unknown error' });
    };

    self.ws.onclose = (event) => {
      log('WARN', 'Disconnected from MCP backend', { code: event.code, reason: event.reason });
      if (!self.reconnectInterval) {
        log('INFO', 'Scheduling reconnection in 5 seconds');
        self.reconnectInterval = setInterval(connectWebSocket, 5000);
      }
    };
  } catch (error) {
    log('ERROR', 'Failed to create WebSocket connection', { error: error.message });
  }
}

// Handle messages from backend
async function handleBackendMessage(message) {
  const { type, requestId, data } = message;

  switch (type) {
    case 'execute':
      await handleExecute(requestId, data);
      break;
    case 'capture_dom':
      await handleCaptureDOM(requestId, data);
      break;
    case 'capture_screenshot':
      await handleCaptureScreenshot(requestId, data);
      break;
    case 'navigate':
      await handleNavigate(requestId, data);
      break;
    case 'new_post':
      await handleLinkedInPost(requestId, data);
      break;
    case 'show_alert':
      await handleAlert(requestId, data);
      break;
    default:
      log('WARN', 'Unknown message type', { type });
  }
}

// Handle execute request
async function handleExecute(requestId, data) {
  log('INFO', 'Handling execute request', { requestId, url: data.url });

  try {
    // Find or create appropriate tab
    const tab = await findOrCreateTab(data.url);

    // Send execute request to content script
    chrome.tabs.sendMessage(tab.id, {
      type: 'execute',
      requestId,
      data
    }, (response) => {
      if (chrome.runtime.lastError) {
        log('ERROR', 'Failed to send execute message', { error: chrome.runtime.lastError.message });
        sendToBackend({
          type: 'request_failed',
          requestId,
          error: chrome.runtime.lastError.message
        });
      } else {
        log('DEBUG', 'Execute message sent', { tabId: tab.id, response });
      }
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Handle DOM capture request
async function handleCaptureDOM(requestId, data) {
  log('INFO', 'Handling DOM capture request', { requestId });

  try {
    // Get active tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (!tab) {
      throw new Error('No active tab found');
    }

    // Send capture request to content script
    chrome.tabs.sendMessage(tab.id, {
      type: 'capture_dom',
      requestId,
      data
    }, (response) => {
      if (chrome.runtime.lastError) {
        log('ERROR', 'Failed to send DOM capture message', { error: chrome.runtime.lastError.message });
        sendToBackend({
          type: 'request_failed',
          requestId,
          error: chrome.runtime.lastError.message
        });
      } else {
        log('DEBUG', 'DOM capture message sent', { tabId: tab.id, response });
      }
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Handle screenshot capture request
async function handleCaptureScreenshot(requestId, data) {
  log('INFO', 'Handling screenshot capture request', { requestId });

  try {
    // Get active tab
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    if (!tab) {
      throw new Error('No active tab found');
    }

    // Send capture request to content script
    chrome.tabs.sendMessage(tab.id, {
      type: 'capture_screenshot',
      requestId,
      data
    }, (response) => {
      if (chrome.runtime.lastError) {
        log('ERROR', 'Failed to send screenshot capture message', { error: chrome.runtime.lastError.message });
        sendToBackend({
          type: 'request_failed',
          requestId,
          error: chrome.runtime.lastError.message
        });
      } else {
        log('DEBUG', 'Screenshot capture message sent', { tabId: tab.id, response });
      }
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Handle navigate request
async function handleNavigate(requestId, data) {
  log('INFO', 'Handling navigate request', { requestId, url: data.url });

  try {
    const tab = await findOrCreateTab(data.url);

    // Send navigate request to content script
    chrome.tabs.sendMessage(tab.id, {
      type: 'navigate',
      requestId,
      data
    }, (response) => {
      if (chrome.runtime.lastError) {
        // If content script not loaded yet, just navigate directly
        chrome.tabs.update(tab.id, { url: data.url }, () => {
          sendToBackend({
            type: 'request_complete',
            requestId,
            result: { message: 'Navigated to ' + data.url }
          });
        });
      } else {
        log('DEBUG', 'Navigate message sent', { tabId: tab.id, response });
      }
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Handle LinkedIn post (legacy)
async function handleLinkedInPost(requestId, data) {
  log('INFO', 'Handling LinkedIn post request', { requestId });

  try {
    const tabs = await chrome.tabs.query({ url: 'https://www.linkedin.com/*' });

    if (tabs.length === 0) {
      throw new Error('No LinkedIn tabs open');
    }

    chrome.tabs.sendMessage(tabs[0].id, {
      action: 'create_post',
      type: 'new_post',
      requestId,
      data
    }, (response) => {
      if (chrome.runtime.lastError) {
        log('ERROR', 'Failed to send post message', { error: chrome.runtime.lastError.message });
        sendToBackend({
          type: 'request_failed',
          requestId,
          error: chrome.runtime.lastError.message
        });
      } else {
        log('DEBUG', 'Post message sent', { tabId: tabs[0].id, response });
      }
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Handle alert
async function handleAlert(requestId, data) {
  log('INFO', 'Handling alert request', { requestId });

  try {
    // Send to active tab or all tabs of target site
    const url = data.targetSite ? `*://*.${data.targetSite}.com/*` : undefined;
    const query = url ? { url } : { active: true, currentWindow: true };
    const tabs = await chrome.tabs.query(query);

    if (tabs.length === 0) {
      throw new Error('No matching tabs found');
    }

    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, {
        action: 'show_alert',
        type: 'show_alert',
        requestId,
        data
      }, (response) => {
        if (chrome.runtime.lastError) {
          log('ERROR', 'Failed to send alert', {
            tabId: tab.id,
            error: chrome.runtime.lastError.message
          });
        } else {
          log('DEBUG', 'Alert sent', { tabId: tab.id, response });
        }
      });
    });
  } catch (error) {
    sendToBackend({
      type: 'request_failed',
      requestId,
      error: error.message
    });
  }
}

// Find or create tab for URL
async function findOrCreateTab(url) {
  if (!url) {
    // Use active tab if no URL specified
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab) throw new Error('No active tab found');
    return tab;
  }

  // Check if tab with URL already exists
  const urlObj = new URL(url);
  const tabs = await chrome.tabs.query({ url: `${urlObj.origin}/*` });

  if (tabs.length > 0) {
    // Focus existing tab
    await chrome.tabs.update(tabs[0].id, { active: true });
    return tabs[0];
  }

  // Create new tab
  return await chrome.tabs.create({ url, active: true });
}

// Send message to backend
function sendToBackend(message) {
  if (self.ws && self.ws.readyState === WebSocket.OPEN) {
    self.ws.send(JSON.stringify(message));
    log('DEBUG', 'Sent to backend', { type: message.type, requestId: message.requestId });
  } else {
    log('ERROR', 'Cannot send to backend - not connected');
  }
}

// Handle screenshot capture
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  log('DEBUG', 'Received message', { action: request.action, sender: sender.tab?.url || 'popup' });

  if (request.action === 'capture_screenshot') {
    chrome.tabs.captureVisibleTab(null, { format: 'png' }, (dataUrl) => {
      if (chrome.runtime.lastError) {
        log('ERROR', 'Failed to capture screenshot', { error: chrome.runtime.lastError.message });
        sendResponse({ error: chrome.runtime.lastError.message });
      } else {
        sendResponse({ screenshot: dataUrl });
      }
    });
    return true; // Keep channel open for async response
  }

  if (request.action === 'check_connection') {
    const connected = self.ws && self.ws.readyState === WebSocket.OPEN;
    sendResponse({ connected });
    return true;
  }

  if (request.action === 'request_complete') {
    log('INFO', 'Request completed', { requestId: request.requestId });
    sendToBackend({
      type: 'request_complete',
      requestId: request.requestId,
      result: request.result
    });
  }

  if (request.action === 'request_failed') {
    log('ERROR', 'Request failed', { requestId: request.requestId, error: request.error });
    sendToBackend({
      type: 'request_failed',
      requestId: request.requestId,
      error: request.error
    });
  }
});

// Initialize
log('INFO', 'Initializing background service worker');
connectWebSocket();

// Lifecycle events
chrome.runtime.onInstalled.addListener(() => {
  log('INFO', 'Extension installed');
  connectWebSocket();
});

chrome.runtime.onStartup.addListener(() => {
  log('INFO', 'Extension started');
  connectWebSocket();
});

// Keep service worker alive
setInterval(() => {
  if (self.ws && self.ws.readyState === WebSocket.OPEN) {
    self.ws.send(JSON.stringify({ type: 'ping' }));
  } else {
    log('DEBUG', 'WebSocket not connected, attempting reconnection');
    connectWebSocket();
  }
}, 30000);