// Content script for flexible browser automation
// This version properly handles code execution in the content script context

// Logger utility
const log = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [CONTENT] [${level}] ${message}`;
  if (data) {
    console.log(logMessage, data);
  } else {
    console.log(logMessage);
  }
};

log('INFO', 'MCP Extension content script loaded', { url: window.location.href });

// Utility functions
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    if (document.querySelector(selector)) {
      return resolve(document.querySelector(selector));
    }

    const observer = new MutationObserver(() => {
      if (document.querySelector(selector)) {
        observer.disconnect();
        resolve(document.querySelector(selector));
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found after ${timeout}ms`));
    }, timeout);
  });
}

// Execute arbitrary JavaScript code - IMPROVED VERSION
async function executeCode(data) {
  const startTime = Date.now();
  try {
    log('INFO', 'Executing JavaScript code', {
      codeLength: data.code?.length,
      url: data.url,
      targetSite: data.targetSite
    });

    // Navigate if URL provided
    if (data.url && window.location.href !== data.url) {
      log('INFO', 'Navigating to URL', { url: data.url });
      window.location.href = data.url;
      // Store the code to execute after navigation
      sessionStorage.setItem('pendingExecution', JSON.stringify(data));
      return { navigating: true };
    }

    // IMPROVED: Execute code directly in content script context
    // This avoids CSP restrictions since content scripts run in an isolated world
    let result;
    
    // Create a sandboxed execution environment with helpful utilities
    const sandbox = {
      // DOM utilities
      document,
      window,
      console,
      
      // Helper functions available to the executed code
      waitForElement,
      waitForSelector: waitForElement, // Alias for compatibility
      log,
      
      // Commonly needed APIs
      querySelector: (selector) => document.querySelector(selector),
      querySelectorAll: (selector) => document.querySelectorAll(selector),
      getElementById: (id) => document.getElementById(id),
      getElementsByClassName: (className) => document.getElementsByClassName(className),
      getElementsByTagName: (tagName) => document.getElementsByTagName(tagName),
      
      // Utility functions
      sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
      
      // Event utilities
      click: (element) => {
        if (typeof element === 'string') {
          element = document.querySelector(element);
        }
        if (element) {
          element.click();
          return true;
        }
        return false;
      },
      
      type: (element, text) => {
        if (typeof element === 'string') {
          element = document.querySelector(element);
        }
        if (element) {
          element.focus();
          element.value = text;
          element.dispatchEvent(new Event('input', { bubbles: true }));
          element.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }
        return false;
      },
      
      // Form utilities
      submitForm: (formSelector) => {
        const form = document.querySelector(formSelector);
        if (form) {
          form.submit();
          return true;
        }
        return false;
      }
    };

    // Detect if the code is a function or expression
    const trimmedCode = data.code.trim();
    const isFunction = trimmedCode.startsWith('function') || 
                      trimmedCode.startsWith('async function') ||
                      trimmedCode.includes('=>');
    
    // Execute the code
    if (isFunction || trimmedCode.includes('return')) {
      // It's a function or has a return statement, wrap and execute
      const wrappedCode = `
        (async function() {
          const { ${Object.keys(sandbox).join(', ')} } = this;
          ${trimmedCode.includes('return') ? trimmedCode : 'return ' + trimmedCode}
        }).call(sandbox)
      `;
      result = await eval(wrappedCode);
    } else {
      // It's a statement or expression, execute directly
      const wrappedCode = `
        (async function() {
          const { ${Object.keys(sandbox).join(', ')} } = this;
          ${trimmedCode}
        }).call(sandbox)
      `;
      result = await eval(wrappedCode);
    }

    const executionTime = Date.now() - startTime;

    const response = {
      value: result,
      metadata: {
        executionTime,
        currentUrl: window.location.href,
        pageTitle: document.title
      }
    };

    return response;
  } catch (error) {
    log('ERROR', 'Code execution failed', { error: error.message, stack: error.stack });
    throw error;
  }
}

// Alternative execution method that detects code patterns
async function smartExecuteCode(data) {
  const code = data.code.trim();
  
  // Pattern detection for common automation tasks
  const patterns = {
    // Click pattern: click("#button") or click('.class')
    click: /^click\s*\(\s*["']([^"']+)["']\s*\)$/,
    
    // Type pattern: type("#input", "text") or type('.class', 'text')
    type: /^type\s*\(\s*["']([^"']+)["']\s*,\s*["']([^"']+)["']\s*\)$/,
    
    // Navigation pattern: navigate("url") or goto("url")
    navigate: /^(?:navigate|goto)\s*\(\s*["']([^"']+)["']\s*\)$/,
    
    // Query pattern: find("#selector") or $(".selector")
    query: /^(?:find|\$)\s*\(\s*["']([^"']+)["']\s*\)$/,
    
    // Wait pattern: wait(1000) or sleep(1000)
    wait: /^(?:wait|sleep)\s*\(\s*(\d+)\s*\)$/
  };
  
  // Check if code matches any pattern
  for (const [action, pattern] of Object.entries(patterns)) {
    const match = code.match(pattern);
    if (match) {
      switch (action) {
        case 'click':
          const clickEl = document.querySelector(match[1]);
          if (clickEl) {
            clickEl.click();
            return { value: true, action: 'clicked', selector: match[1] };
          }
          return { value: false, error: `Element not found: ${match[1]}` };
          
        case 'type':
          const typeEl = document.querySelector(match[1]);
          if (typeEl) {
            typeEl.focus();
            typeEl.value = match[2];
            typeEl.dispatchEvent(new Event('input', { bubbles: true }));
            typeEl.dispatchEvent(new Event('change', { bubbles: true }));
            return { value: true, action: 'typed', selector: match[1], text: match[2] };
          }
          return { value: false, error: `Element not found: ${match[1]}` };
          
        case 'navigate':
          window.location.href = match[1];
          return { value: true, action: 'navigating', url: match[1] };
          
        case 'query':
          const elements = document.querySelectorAll(match[1]);
          return { 
            value: elements.length, 
            action: 'found', 
            selector: match[1],
            elements: Array.from(elements).map(el => ({
              tag: el.tagName,
              text: el.textContent.substring(0, 100),
              id: el.id,
              className: el.className
            }))
          };
          
        case 'wait':
          await new Promise(resolve => setTimeout(resolve, parseInt(match[1])));
          return { value: true, action: 'waited', duration: match[1] };
      }
    }
  }
  
  // If no pattern matched, fall back to regular execution
  return executeCode(data);
}

// Rest of the file remains the same (captureDOM, captureScreenshot, etc.)
// ... (keeping all the other functions from the original file)

// Message handler - Updated to use the improved execution
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  log('DEBUG', 'Received message', { action: request.action, type: request.type });

  // Handle new flexible requests
  if (request.type === 'execute') {
    // Use smart execution that detects patterns
    smartExecuteCode(request.data).then(result => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { result }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  }
  // ... rest of the message handlers remain the same
  
  return true;
});