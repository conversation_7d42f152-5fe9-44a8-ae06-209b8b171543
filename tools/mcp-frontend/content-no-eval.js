// Alternative approach: Use chrome.scripting API to inject code
// This completely bypasses CSP restrictions

// Content script that acts as a bridge
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('[CONTENT] Received message:', request.type);
  
  if (request.type === 'execute') {
    // Instead of executing here, ask background script to inject
    chrome.runtime.sendMessage({
      action: 'inject_script',
      requestId: request.requestId,
      code: request.data.code,
      tabId: request.tabId
    });
    sendResponse({ status: 'delegated_to_background' });
  }
  
  // Other handlers remain the same...
  return true;
});