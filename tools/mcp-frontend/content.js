// Content script for flexible browser automation

// Logger utility
const log = (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [CONTENT] [${level}] ${message}`;
  if (data) {
    console.log(logMessage, data);
  } else {
    console.log(logMessage);
  }
};

log('INFO', 'MCP Extension content script loaded', { url: window.location.href });

// Pattern matching for common operations (no eval needed)
function executePatternMatch(code) {
  const trimmed = code.trim();
  
  // Simple property access
  if (trimmed === 'document.title') return { value: document.title };
  if (trimmed === 'document.URL' || trimmed === 'window.location.href') return { value: window.location.href };
  
  // Click pattern: click('.button') or click("#id")
  const clickMatch = trimmed.match(/^click\s*\(\s*['"`]([^'"`]+)['"`]\s*\)$/);
  if (clickMatch) {
    const el = document.querySelector(clickMatch[1]);
    if (el) {
      el.click();
      return { value: true, action: 'clicked', selector: clickMatch[1] };
    }
    return { value: false, error: `Element not found: ${clickMatch[1]}` };
  }
  
  // Type pattern: type('#input', 'text')
  const typeMatch = trimmed.match(/^type\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*\)$/);
  if (typeMatch) {
    const el = document.querySelector(typeMatch[1]);
    if (el) {
      el.focus();
      el.value = typeMatch[2];
      el.dispatchEvent(new Event('input', { bubbles: true }));
      el.dispatchEvent(new Event('change', { bubbles: true }));
      return { value: true, action: 'typed', selector: typeMatch[1], text: typeMatch[2] };
    }
    return { value: false, error: `Element not found: ${typeMatch[1]}` };
  }
  
  // Query pattern: querySelectorAll('.class').length
  const queryMatch = trimmed.match(/^querySelectorAll\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\.length$/);
  if (queryMatch) {
    const elements = document.querySelectorAll(queryMatch[1]);
    return { value: elements.length };
  }
  
  // Return null if no pattern matched
  return null;
}

// Inject script tag method (works on some sites)
async function injectScriptTag(code, sandbox) {
  return new Promise((resolve, reject) => {
    const id = 'mcp-exec-' + Date.now();
    const script = document.createElement('script');
    script.textContent = `
      (async function() {
        try {
          const result = await (async function() {
            const { ${Object.keys(sandbox).join(', ')} } = window.__mcpSandbox;
            ${code}
          })();
          window.__mcpResult_${id} = { success: true, result };
        } catch (e) {
          window.__mcpResult_${id} = { success: false, error: e.message };
        }
      })();
    `;
    
    window.__mcpSandbox = sandbox;
    document.documentElement.appendChild(script);
    script.remove();
    
    setTimeout(() => {
      const result = window[`__mcpResult_${id}`];
      delete window.__mcpSandbox;
      delete window[`__mcpResult_${id}`];
      
      if (result && result.success) {
        resolve(result.result);
      } else {
        reject(new Error(result ? result.error : 'Script injection failed'));
      }
    }, 100);
  });
}

// Utility functions
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    if (document.querySelector(selector)) {
      return resolve(document.querySelector(selector));
    }

    const observer = new MutationObserver(() => {
      if (document.querySelector(selector)) {
        observer.disconnect();
        resolve(document.querySelector(selector));
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found after ${timeout}ms`));
    }, timeout);
  });
}

// Execute arbitrary JavaScript code
async function executeCode(data) {
  const startTime = Date.now();
  try {
    log('INFO', 'Executing JavaScript code', {
      codeLength: data.code?.length,
      url: data.url,
      targetSite: data.targetSite
    });

    // Navigate if URL provided
    if (data.url && window.location.href !== data.url) {
      log('INFO', 'Navigating to URL', { url: data.url });
      window.location.href = data.url;
      // Store the code to execute after navigation
      sessionStorage.setItem('pendingExecution', JSON.stringify(data));
      return { navigating: true };
    }

    // FIXED: Execute code directly in content script context using eval
    // This works because content scripts run in an isolated world
    // The page's CSP does NOT apply to the content script's context
    
    // Create a sandboxed environment with utilities
    const sandbox = {
      // DOM utilities
      document,
      window,
      console,
      
      // Helper functions
      waitForElement,
      waitForSelector: waitForElement,
      log,
      
      // Commonly needed APIs
      querySelector: (sel) => document.querySelector(sel),
      querySelectorAll: (sel) => document.querySelectorAll(sel),
      
      // Utility functions
      sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
      
      // Action helpers
      click: (element) => {
        if (typeof element === 'string') {
          element = document.querySelector(element);
        }
        if (element) {
          element.click();
          return true;
        }
        return false;
      },
      
      type: (element, text) => {
        if (typeof element === 'string') {
          element = document.querySelector(element);
        }
        if (element) {
          element.focus();
          element.value = text;
          element.dispatchEvent(new Event('input', { bubbles: true }));
          element.dispatchEvent(new Event('change', { bubbles: true }));
          return true;
        }
        return false;
      }
    };

    // Execute the code - try multiple methods to bypass CSP
    let result;
    
    // First, try pattern matching for common operations
    result = executePatternMatch(data.code);
    if (result !== null) {
      return result;
    }
    
    // If no pattern matched, try Function constructor
    try {
      const AsyncFunction = (async function() {}).constructor;
      const fn = new AsyncFunction(...Object.keys(sandbox), data.code);
      result = await fn(...Object.values(sandbox));
    } catch (error) {
      // If Function constructor fails, try injecting a script tag
      try {
        result = await injectScriptTag(data.code, sandbox);
      } catch (scriptError) {
        // Last resort: return the error
        throw new Error(`Code execution blocked by CSP. Error: ${error.message}`);
      }
    }

    const executionTime = Date.now() - startTime;

    const response = {
      value: result,
      metadata: {
        executionTime,
        currentUrl: window.location.href,
        pageTitle: document.title
      }
    };

    return response;
  } catch (error) {
    log('ERROR', 'Code execution failed', { error: error.message, stack: error.stack });
    throw error;
  }
}

// Capture DOM content
async function captureDOM(data) {
  try {
    log('INFO', 'Capturing DOM', data);

    const response = {};

    // Capture metadata if requested
    if (data.includeMetadata !== false) {
      response.metadata = {
        currentUrl: window.location.href,
        pageTitle: document.title,
        timestamp: new Date().toISOString()
      };
    }

    // Capture DOM
    if (data.selector) {
      const element = document.querySelector(data.selector);
      if (element) {
        response.dom = {
          html: element.outerHTML,
          text: element.textContent,
          selector: data.selector
        };
      } else {
        throw new Error(`Element not found: ${data.selector}`);
      }
    } else {
      response.dom = {
        body: document.body.innerHTML,
        title: document.title,
        url: window.location.href
      };
    }

    return response;
  } catch (error) {
    log('ERROR', 'DOM capture failed', { error: error.message });
    throw error;
  }
}

// Capture screenshot
async function captureScreenshotData(data) {
  try {
    log('INFO', 'Capturing screenshot', data);

    const response = {};

    // Capture screenshot with options
    response.screenshot = await captureScreenshot(data.selector, data.fullPage, data.quality);

    // Add metadata
    response.metadata = {
      currentUrl: window.location.href,
      pageTitle: document.title,
      timestamp: new Date().toISOString(),
      selector: data.selector || 'full page'
    };

    return response;
  } catch (error) {
    log('ERROR', 'Screenshot capture failed', { error: error.message });
    throw error;
  }
}

// Navigate to URL
async function navigateToUrl(data) {
  try {
    log('INFO', 'Navigating to URL', data);

    // Store wait condition if provided
    if (data.waitFor) {
      sessionStorage.setItem('navigationWaitFor', data.waitFor);
    }

    window.location.href = data.url;
    return { navigating: true };
  } catch (error) {
    log('ERROR', 'Navigation failed', { error: error.message });
    throw error;
  }
}

// Capture screenshot using Chrome API
async function captureScreenshot(selector, fullPage, quality) {
  return new Promise((resolve, reject) => {
    // Request screenshot from background script
    chrome.runtime.sendMessage({
      action: 'capture_screenshot',
      selector: selector,
      fullPage: fullPage,
      quality: quality
    }, (response) => {
      if (response && response.screenshot) {
        resolve(response.screenshot);
      } else {
        reject(new Error('Failed to capture screenshot'));
      }
    });
  });
}

// Legacy LinkedIn functions (kept for backward compatibility)
async function createLinkedInPost(postContent) {
  try {
    log('INFO', 'Starting LinkedIn post creation', { contentLength: postContent.content.length });

    // Check if we're on the LinkedIn feed page
    if (!window.location.href.includes('linkedin.com/feed')) {
      log('INFO', 'Not on feed page, navigating to LinkedIn feed');
      window.location.href = 'https://www.linkedin.com/feed/';
      sessionStorage.setItem('pendingPost', JSON.stringify(postContent));
      return;
    }

    // Execute LinkedIn-specific code
    const code = `
      // Find and click Start Post button
      const startPostButton = await waitForSelector(
        '[data-test-id="share-box-feed-entry-trigger"], ' +
        '.share-box-feed-entry__trigger, ' +
        'button[aria-label*="Start a post"]'
      );
      startPostButton.click();

      // Wait for editor
      const postEditor = await waitForSelector('.ql-editor');
      postEditor.focus();
      postEditor.click();

      // Set content
      document.execCommand('insertText', false, '${postContent.content.replace(/'/g, "\\'")}');

      // Find and click Post button
      await new Promise(resolve => setTimeout(resolve, 1000));
      const postButton = await waitForSelector('button[data-test-id="share-post-button"]');

      // Wait for button to be enabled
      let attempts = 0;
      while (postButton.disabled && attempts < 10) {
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }

      if (!postButton.disabled) {
        postButton.click();
        return { success: true, message: 'Post created successfully' };
      } else {
        throw new Error('Post button remained disabled');
      }
    `;

    const result = await executeCode({ code });
    return result;
  } catch (error) {
    log('ERROR', 'Failed to create post', { error: error.message });
    throw error;
  }
}

function showLinkedInAlert(alertData) {
  log('INFO', 'Showing alert', alertData);

  const alertDiv = document.createElement('div');
  alertDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #0077B5;
    color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    max-width: 400px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    animation: slideIn 0.3s ease-out;
  `;

  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(400px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `;
  document.head.appendChild(style);

  alertDiv.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: start;">
      <div>
        <h3 style="margin: 0 0 10px 0; font-size: 16px; font-weight: 600;">
          ${alertData.title || 'MCP Alert'}
        </h3>
        <p style="margin: 0; font-size: 14px; line-height: 1.5;">
          ${alertData.message}
        </p>
      </div>
      <button style="
        background: none; border: none; color: white;
        font-size: 20px; cursor: pointer; padding: 0;
        margin-left: 15px; line-height: 1;
      ">&times;</button>
    </div>
  `;

  const closeButton = alertDiv.querySelector('button');
  closeButton.addEventListener('click', () => alertDiv.remove());

  document.body.appendChild(alertDiv);

  setTimeout(() => {
    if (alertDiv.parentNode) alertDiv.remove();
  }, 5000);
}

// Check for pending operations after navigation
window.addEventListener('load', () => {
  // Check for pending code execution
  const pendingExecution = sessionStorage.getItem('pendingExecution');
  if (pendingExecution) {
    log('INFO', 'Found pending execution after navigation');
    const data = JSON.parse(pendingExecution);
    sessionStorage.removeItem('pendingExecution');

    // Wait a bit for page to stabilize
    setTimeout(async () => {
      try {
        const result = await executeCode(data);
        // Send result back to background
        chrome.runtime.sendMessage({
          action: 'execution_complete',
          result: result
        });
      } catch (error) {
        chrome.runtime.sendMessage({
          action: 'execution_failed',
          error: error.message
        });
      }
    }, 2000);
  }

  // Check for pending LinkedIn post
  const pendingPost = sessionStorage.getItem('pendingPost');
  if (pendingPost && window.location.href.includes('linkedin.com/feed')) {
    log('INFO', 'Found pending post after navigation');
    const postData = JSON.parse(pendingPost);
    sessionStorage.removeItem('pendingPost');
    setTimeout(() => createLinkedInPost(postData), 2000);
  }

  // Check for navigation wait condition
  const waitFor = sessionStorage.getItem('navigationWaitFor');
  if (waitFor) {
    sessionStorage.removeItem('navigationWaitFor');
    // Implement wait logic here if needed
  }
});

// Message handler
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  log('DEBUG', 'Received message', { action: request.action, type: request.type });

  // Handle new flexible requests
  if (request.type === 'execute') {
    executeCode(request.data).then(result => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { result }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  } else if (request.type === 'capture_dom') {
    captureDOM(request.data).then(result => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { result }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  } else if (request.type === 'capture_screenshot') {
    captureScreenshotData(request.data).then(result => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { result }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  } else if (request.type === 'navigate') {
    navigateToUrl(request.data).then(result => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { message: 'Navigation initiated' }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  }

  // Handle legacy LinkedIn requests
  else if (request.action === 'create_post' || request.type === 'new_post') {
    createLinkedInPost(request.data).then(() => {
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { message: 'Post created successfully' }
      });
    }).catch(error => {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
    });
    sendResponse({ status: 'processing' });
  } else if (request.action === 'show_alert' || request.type === 'show_alert') {
    try {
      showLinkedInAlert(request.data);
      chrome.runtime.sendMessage({
        action: 'request_complete',
        requestId: request.requestId,
        result: { message: 'Alert shown successfully' }
      });
      sendResponse({ status: 'alert_shown' });
    } catch (error) {
      chrome.runtime.sendMessage({
        action: 'request_failed',
        requestId: request.requestId,
        error: error.message
      });
      sendResponse({ status: 'error', error: error.message });
    }
  }

  return true;
});