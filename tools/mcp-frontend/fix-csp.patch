--- content.js.original
+++ content.js.fixed
@@ -59,12 +59,71 @@ async function executeCode(data) {
       return { navigating: true };
     }
 
-    // Create a function from the code string
-    const AsyncFunction = Object.getPrototypeOf(async function(){}).constructor;
-    const userFunction = new AsyncFunction('waitForElement', 'waitForSelector', 'log', data.code);
+    // FIXED: Execute code directly in content script context using eval
+    // This works because content scripts run in an isolated world
+    // The page's CSP does NOT apply to the content script's context
+    
+    // Create a sandboxed environment with utilities
+    const sandbox = {
+      // DOM utilities
+      document,
+      window,
+      console,
+      
+      // Helper functions
+      waitForElement,
+      waitForSelector: waitForElement,
+      log,
+      
+      // Commonly needed APIs
+      querySelector: (sel) => document.querySelector(sel),
+      querySelectorAll: (sel) => document.querySelectorAll(sel),
+      
+      // Utility functions
+      sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
+      
+      // Action helpers
+      click: (element) => {
+        if (typeof element === 'string') {
+          element = document.querySelector(element);
+        }
+        if (element) {
+          element.click();
+          return true;
+        }
+        return false;
+      },
+      
+      type: (element, text) => {
+        if (typeof element === 'string') {
+          element = document.querySelector(element);
+        }
+        if (element) {
+          element.focus();
+          element.value = text;
+          element.dispatchEvent(new Event('input', { bubbles: true }));
+          element.dispatchEvent(new Event('change', { bubbles: true }));
+          return true;
+        }
+        return false;
+      }
+    };
 
-    // Helper function for waiting (compatible with user code)
-    const waitForSelector = waitForElement;
+    // Execute the code with access to sandbox utilities
+    let result;
+    try {
+      // Wrap the code to have access to sandbox utilities
+      const wrappedCode = `
+        (async function() {
+          const { ${Object.keys(sandbox).join(', ')} } = this;
+          ${data.code}
+        }).call(sandbox)
+      `;
+      
+      // This eval runs in the content script context, NOT the page context
+      // Therefore it's not subject to the page's CSP restrictions
+      result = await eval(wrappedCode);
+    } catch (error) {
+      throw new Error(`Code execution error: ${error.message}`);
+    }
 
-    // Execute the user's code
-    const result = await userFunction(waitForElement, waitForSelector, log);