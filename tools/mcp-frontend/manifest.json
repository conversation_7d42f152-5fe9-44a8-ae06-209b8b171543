{"manifest_version": 3, "name": "Browser Automation MCP Extension", "version": "2.0.0", "description": "Flexible browser automation extension for MCP", "permissions": ["activeTab", "storage", "tabs", "scripting", "webNavigation"], "host_permissions": ["<all_urls>", "http://localhost:3636/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup.html", "default_icon": {"16": "icon-16.png", "48": "icon-48.png", "128": "icon-128.png"}}, "icons": {"16": "icon-16.png", "48": "icon-48.png", "128": "icon-128.png"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}