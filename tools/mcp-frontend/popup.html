<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 350px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    
    h1 {
      font-size: 18px;
      margin-bottom: 15px;
    }
    
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      font-size: 14px;
    }
    
    .status.connected {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.disconnected {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    button {
      background-color: #0077b5;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
      width: 100%;
      margin-bottom: 10px;
    }
    
    button:hover {
      background-color: #005885;
    }
    
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    .post-info {
      background-color: #f0f0f0;
      padding: 10px;
      border-radius: 5px;
      margin-top: 15px;
      font-size: 12px;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  </style>
</head>
<body>
  <h1>LinkedIn MCP Extension</h1>
  
  <div id="status" class="status disconnected">
    Checking connection...
  </div>
  
  <button id="getLatestPost" disabled>Get Latest Post</button>
  <button id="testPost" disabled>Test Create Post</button>
  
  <div id="postInfo" class="post-info" style="display: none;"></div>
  
  <script src="popup.js"></script>
</body>
</html>