// Popup script for extension UI

const statusDiv = document.getElementById('status');
const getLatestPostBtn = document.getElementById('getLatestPost');
const testPostBtn = document.getElementById('testPost');
const postInfoDiv = document.getElementById('postInfo');

// Check connection status
function checkConnection() {
  chrome.runtime.sendMessage({ action: 'check_connection' }, (response) => {
    if (response && response.connected) {
      statusDiv.textContent = 'Connected to MCP Backend';
      statusDiv.className = 'status connected';
      getLatestPostBtn.disabled = false;
      testPostBtn.disabled = false;
    } else {
      statusDiv.textContent = 'Disconnected from MCP Backend';
      statusDiv.className = 'status disconnected';
      getLatestPostBtn.disabled = true;
      testPostBtn.disabled = true;
    }
  });
}

// Get latest post from backend
getLatestPostBtn.addEventListener('click', () => {
  getLatestPostBtn.disabled = true;
  getLatestPostBtn.textContent = 'Loading...';
  
  chrome.runtime.sendMessage({ action: 'get_latest_post' }, (response) => {
    getLatestPostBtn.disabled = false;
    getLatestPostBtn.textContent = 'Get Latest Post';
    
    if (response && response.data) {
      postInfoDiv.style.display = 'block';
      postInfoDiv.textContent = `Latest Post:\n\nContent: ${response.data.content}\nTime: ${new Date(response.data.timestamp).toLocaleString()}`;
    } else if (response && response.error) {
      postInfoDiv.style.display = 'block';
      postInfoDiv.textContent = `Error: ${response.error}`;
    } else {
      postInfoDiv.style.display = 'block';
      postInfoDiv.textContent = 'No posts available';
    }
  });
});

// Test creating a post
testPostBtn.addEventListener('click', () => {
  // Get the active tab
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    if (tabs[0].url.includes('linkedin.com')) {
      // Send test post data
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'create_post',
        data: {
          content: `Test post from LinkedIn MCP Extension - ${new Date().toLocaleTimeString()}`,
          timestamp: new Date()
        }
      });
      
      postInfoDiv.style.display = 'block';
      postInfoDiv.textContent = 'Creating test post...';
    } else {
      postInfoDiv.style.display = 'block';
      postInfoDiv.textContent = 'Please navigate to LinkedIn.com first';
    }
  });
});

// Check connection on load
checkConnection();

// Recheck every 2 seconds
setInterval(checkConnection, 2000);