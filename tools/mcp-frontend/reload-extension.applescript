-- AppleScript to reload Chrome extension
-- Requires Chrome to be open with extensions page

tell application "Google Chrome"
    activate
    
    -- Open extensions page if not already open
    set extensionsURL to "chrome://extensions/"
    set foundTab to false
    
    repeat with w in windows
        repeat with t in tabs of w
            if URL of t starts with extensionsURL then
                set active tab index of w to index of t
                set foundTab to true
                exit repeat
            end if
        end repeat
        if foundTab then exit repeat
    end repeat
    
    if not foundTab then
        tell window 1
            set newTab to make new tab with properties {URL:extensionsURL}
        end tell
        delay 2
    end if
    
    -- Enable developer mode if needed (keyboard shortcut)
    tell application "System Events"
        tell process "Google Chrome"
            -- Press Cmd+R to reload the page (this reloads all extensions)
            keystroke "r" using command down
            delay 1
        end tell
    end tell
end tell