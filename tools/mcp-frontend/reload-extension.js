// <PERSON><PERSON><PERSON> to reload the MCP extension
// This should be run in the Chrome extensions page console

// Find the MCP extension
const extensions = document.querySelectorAll('extensions-item');
let mcpExtension = null;

extensions.forEach(ext => {
  const nameElement = ext.shadowRoot.querySelector('#name-and-version .name');
  if (nameElement && nameElement.textContent.includes('Browser Automation MCP')) {
    mcpExtension = ext;
  }
});

if (mcpExtension) {
  // Click the reload button
  const reloadButton = mcpExtension.shadowRoot.querySelector('#dev-reload-button');
  if (reloadButton) {
    reloadButton.click();
    console.log('Extension reloaded successfully!');
  } else {
    console.log('Reload button not found. Is developer mode enabled?');
  }
} else {
  console.log('MCP Extension not found');
}