<!DOCTYPE html>
<html>
<head>
    <title>Extension Reload Helper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1d4ed8;
        }
        .info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            color: #ef4444;
        }
        .success {
            color: #10b981;
        }
    </style>
</head>
<body>
    <h1>Extension Reload Helper</h1>
    
    <div class="info">
        <p>This page helps reload the Chrome extension. The extension has been updated to use pattern matching instead of eval for JavaScript execution.</p>
        <p>Click the button below to open the extensions page where you can manually reload the extension.</p>
    </div>
    
    <button onclick="openExtensionsPage()">Open Extensions Page</button>
    <button onclick="testAlert()">Test Alert (after reload)</button>
    
    <div id="status"></div>
    
    <div class="info">
        <h3>Manual Reload Steps:</h3>
        <ol>
            <li>Click "Open Extensions Page"</li>
            <li>Find "Browser Automation MCP Extension"</li>
            <li>Click the refresh icon on the extension card</li>
            <li>Come back here and click "Test Alert"</li>
        </ol>
    </div>
    
    <script>
        function openExtensionsPage() {
            window.open('chrome://extensions/', '_blank');
            document.getElementById('status').innerHTML = '<p class="success">Extensions page opened. Please manually reload the extension.</p>';
        }
        
        function testAlert() {
            // This will test if the extension can show alerts
            try {
                // Send message to extension
                if (window.chrome && window.chrome.runtime) {
                    document.getElementById('status').innerHTML = '<p class="success">Alert test: If you see an alert, the extension is working!</p>';
                    alert('Extension test successful!');
                } else {
                    document.getElementById('status').innerHTML = '<p class="error">Chrome extension API not available</p>';
                }
            } catch (e) {
                document.getElementById('status').innerHTML = '<p class="error">Error: ' + e.message + '</p>';
            }
        }
    </script>
</body>
</html>