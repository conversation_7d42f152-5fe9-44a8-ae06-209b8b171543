// Safe execution approach for strict CSP environments
// This version uses NO eval, NO Function constructor, NO dynamic code execution

function safeExecuteCode(code) {
  // Parse the code to understand what it wants to do
  const trimmed = code.trim();
  
  // Property access patterns
  const propertyPatterns = {
    'document.title': () => document.title,
    'window.location.href': () => window.location.href,
    'document.URL': () => document.URL,
    'document.body.innerText': () => document.body.innerText,
    'document.body.innerHTML.length': () => document.body.innerHTML.length,
  };
  
  // Check exact matches first
  if (propertyPatterns[trimmed]) {
    return { value: propertyPatterns[trimmed]() };
  }
  
  // Method patterns with arguments
  const patterns = [
    {
      // querySelector: document.querySelector('.class')
      regex: /^document\.querySelector\(['"`]([^'"`]+)['"`]\)$/,
      execute: (match) => {
        const el = document.querySelector(match[1]);
        return { 
          value: el ? { found: true, tag: el.tagName, id: el.id, className: el.className } : null 
        };
      }
    },
    {
      // querySelectorAll: document.querySelectorAll('.class')
      regex: /^document\.querySelectorAll\(['"`]([^'"`]+)['"`]\)$/,
      execute: (match) => {
        const els = document.querySelectorAll(match[1]);
        return { 
          value: Array.from(els).map(el => ({
            tag: el.tagName,
            id: el.id,
            className: el.className,
            text: el.textContent.substring(0, 50)
          }))
        };
      }
    },
    {
      // Click: element.click() or click('#id')
      regex: /^(?:document\.querySelector\(['"`]([^'"`]+)['"`]\)\.click\(\)|click\(['"`]([^'"`]+)['"`]\))$/,
      execute: (match) => {
        const selector = match[1] || match[2];
        const el = document.querySelector(selector);
        if (el) {
          el.click();
          return { value: true, action: 'clicked', selector };
        }
        return { value: false, error: `Element not found: ${selector}` };
      }
    },
    {
      // Type: element.value = 'text' or type('#id', 'text')
      regex: /^(?:document\.querySelector\(['"`]([^'"`]+)['"`]\)\.value\s*=\s*['"`]([^'"`]+)['"`]|type\(['"`]([^'"`]+)['"`],\s*['"`]([^'"`]+)['"`]\))$/,
      execute: (match) => {
        const selector = match[1] || match[3];
        const text = match[2] || match[4];
        const el = document.querySelector(selector);
        if (el) {
          el.focus();
          el.value = text;
          el.dispatchEvent(new Event('input', { bubbles: true }));
          el.dispatchEvent(new Event('change', { bubbles: true }));
          return { value: true, action: 'typed', selector, text };
        }
        return { value: false, error: `Element not found: ${selector}` };
      }
    },
    {
      // Count: document.querySelectorAll('.class').length
      regex: /^document\.querySelectorAll\(['"`]([^'"`]+)['"`]\)\.length$/,
      execute: (match) => {
        const count = document.querySelectorAll(match[1]).length;
        return { value: count };
      }
    },
    {
      // Get text: document.querySelector('.class').textContent
      regex: /^document\.querySelector\(['"`]([^'"`]+)['"`]\)\.(textContent|innerText)$/,
      execute: (match) => {
        const el = document.querySelector(match[1]);
        return { value: el ? el[match[2]] : null };
      }
    }
  ];
  
  // Try each pattern
  for (const pattern of patterns) {
    const match = trimmed.match(pattern.regex);
    if (match) {
      return pattern.execute(match);
    }
  }
  
  // Complex code analysis
  if (trimmed.includes('return')) {
    // Extract what should be returned
    const returnMatch = trimmed.match(/return\s+(.+?)(?:;|$)/);
    if (returnMatch) {
      const returnExpression = returnMatch[1].trim();
      
      // Try to evaluate the return expression using patterns
      if (propertyPatterns[returnExpression]) {
        return { value: propertyPatterns[returnExpression]() };
      }
      
      // Handle object returns
      if (returnExpression.startsWith('{') && returnExpression.endsWith('}')) {
        try {
          // Parse simple object returns like: { title: document.title, url: window.location.href }
          const obj = {};
          const props = returnExpression.slice(1, -1).split(',');
          
          for (const prop of props) {
            const [key, value] = prop.split(':').map(s => s.trim());
            if (propertyPatterns[value]) {
              obj[key] = propertyPatterns[value]();
            }
          }
          
          return { value: obj };
        } catch (e) {
          // Fallback
        }
      }
    }
  }
  
  // If nothing matched, return error
  return { 
    error: 'Code pattern not recognized. Use simple patterns like: document.title, click("#button"), type("#input", "text"), document.querySelectorAll(".class").length',
    code: trimmed
  };
}

// Export for use in content script
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { safeExecuteCode };
}