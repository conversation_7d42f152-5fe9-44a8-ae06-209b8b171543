// Test different execution methods

// Method 1: Direct property access (no eval)
function executeWithoutEval(code) {
  // Parse simple commands without eval
  const commands = {
    'document.title': () => document.title,
    'document.url': () => document.URL,
    'countLinks': () => document.querySelectorAll('a').length,
    'getButtons': () => {
      const buttons = document.querySelectorAll('button, [type="submit"]');
      return Array.from(buttons).map(b => ({
        text: b.textContent.trim(),
        id: b.id,
        className: b.className
      }));
    }
  };
  
  // Check if it's a simple command
  for (const [cmd, fn] of Object.entries(commands)) {
    if (code.includes(cmd)) {
      return fn();
    }
  }
  
  // For complex code, use a different approach
  return executeComplexCode(code);
}

// Method 2: Use Function constructor in a try-catch
function executeComplexCode(code) {
  try {
    // Try Function constructor first
    const fn = new Function('document', 'window', `
      const querySelector = (s) => document.querySelector(s);
      const querySelectorAll = (s) => document.querySelectorAll(s);
      ${code}
    `);
    return fn(document, window);
  } catch (e1) {
    try {
      // If that fails, try creating a script element
      const script = document.createElement('script');
      script.textContent = `
        (function() {
          const __result = (function() { ${code} })();
          window.__mcpResult = __result;
        })();
      `;
      document.head.appendChild(script);
      script.remove();
      const result = window.__mcpResult;
      delete window.__mcpResult;
      return result;
    } catch (e2) {
      // Last resort: return error
      return { error: 'Cannot execute code due to CSP restrictions', originalError: e1.message };
    }
  }
}

// Method 3: Pattern matching for common operations
function executePattern(code) {
  // Click pattern
  const clickMatch = code.match(/click\(['"]([^'"]+)['"]\)/);
  if (clickMatch) {
    const element = document.querySelector(clickMatch[1]);
    if (element) {
      element.click();
      return { clicked: true, selector: clickMatch[1] };
    }
    return { clicked: false, error: 'Element not found' };
  }
  
  // Type pattern
  const typeMatch = code.match(/type\(['"]([^'"]+)['"],\s*['"]([^'"]+)['"]\)/);
  if (typeMatch) {
    const element = document.querySelector(typeMatch[1]);
    if (element) {
      element.value = typeMatch[2];
      element.dispatchEvent(new Event('input', { bubbles: true }));
      return { typed: true, selector: typeMatch[1], value: typeMatch[2] };
    }
    return { typed: false, error: 'Element not found' };
  }
  
  // Query pattern
  const queryMatch = code.match(/document\.querySelectorAll\(['"]([^'"]+)['"]\)/);
  if (queryMatch) {
    const elements = document.querySelectorAll(queryMatch[1]);
    return {
      found: elements.length,
      elements: Array.from(elements).slice(0, 10).map(el => ({
        tag: el.tagName,
        text: el.textContent.substring(0, 50)
      }))
    };
  }
  
  return null;
}