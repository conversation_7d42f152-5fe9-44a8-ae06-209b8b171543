<!DOCTYPE html>
<html>
<head>
    <title>MCP Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            background: #f3f4f6;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #10b981;
        }
        .error {
            border-left: 4px solid #ef4444;
        }
    </style>
</head>
<body>
    <h1>MCP Extension Test Page</h1>
    <p>This page tests different aspects of the MCP browser extension.</p>

    <h2>Test Controls</h2>
    <button class="test-button" onclick="testSimpleAlert()">Test Simple Alert</button>
    <button class="test-button" onclick="testDOMAccess()">Test DOM Access</button>
    <button class="test-button" onclick="testConsoleLog()">Test Console Log</button>
    <button class="test-button" onclick="checkExtension()">Check Extension Status</button>

    <h2>Results</h2>
    <div id="results"></div>

    <script>
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function testSimpleAlert() {
            try {
                alert('This is a native browser alert - it should work!');
                addResult('Native alert executed successfully');
            } catch (e) {
                addResult(`Native alert failed: ${e.message}`, false);
            }
        }

        function testDOMAccess() {
            try {
                const title = document.title;
                const elementCount = document.querySelectorAll('*').length;
                addResult(`DOM Access successful: Title="${title}", Elements=${elementCount}`);
            } catch (e) {
                addResult(`DOM access failed: ${e.message}`, false);
            }
        }

        function testConsoleLog() {
            console.log('[TEST] Console log from test page');
            addResult('Check browser console for "[TEST] Console log from test page"');
        }

        function checkExtension() {
            // Check if content script is injected
            if (window.__mcpExtensionLoaded) {
                addResult('MCP Extension content script is loaded');
            } else {
                addResult('MCP Extension content script NOT detected', false);
            }

            // Check for extension ID in error messages
            const scripts = document.querySelectorAll('script');
            addResult(`Found ${scripts.length} script tags on page`);
        }

        // Auto-check on load
        window.addEventListener('load', () => {
            addResult('Test page loaded');
            
            // Try to detect content script
            setTimeout(() => {
                if (document.querySelector('[data-mcp-extension]')) {
                    addResult('MCP Extension markers found');
                }
            }, 1000);
        });
    </script>
</body>
</html>