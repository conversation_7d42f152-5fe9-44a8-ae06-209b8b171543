#!/bin/bash

# Update Submodules Script
# Updates all submodules to the latest commits on their tracked branches

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    Updating Submodules                      ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Function to check if we're in the root directory
check_root_directory() {
    if [[ ! -f ".gitmodules" ]]; then
        echo -e "${RED}❌ Error: .gitmodules file not found${NC}"
        echo -e "${YELLOW}Please run this script from the root of the repository${NC}"
        exit 1
    fi
}

# Function to show current submodule status
show_submodule_status() {
    echo -e "${BLUE}📋 Current submodule status:${NC}"
    git submodule status
    echo ""
}

# Function to update main repository
update_main_repo() {
    echo -e "${BLUE}🔄 Updating main repository...${NC}"
    
    # Check if there are uncommitted changes
    if ! git diff-index --quiet HEAD --; then
        echo -e "${YELLOW}⚠️  Warning: You have uncommitted changes in the main repository${NC}"
        echo -e "${YELLOW}Please commit or stash them before updating submodules${NC}"
        exit 1
    fi
    
    git pull
    echo -e "${GREEN}✅ Main repository updated${NC}"
    echo ""
}

# Function to update submodules to latest branch commits
update_submodules() {
    echo -e "${BLUE}🔄 Updating submodules to latest branch commits...${NC}"
    
    # Update all submodules to their latest tracked branch commits
    git submodule update --remote
    
    echo -e "${GREEN}✅ Submodules updated to latest commits${NC}"
    echo ""
}

# Function to show what changed
show_changes() {
    echo -e "${BLUE}📊 Submodule changes:${NC}"
    
    # Check if there are any changes to commit
    if git diff --cached --quiet && git diff --quiet; then
        echo -e "${GREEN}✅ No submodule updates available${NC}"
        return 0
    fi
    
    # Show the changes
    git status --porcelain | grep "^M " | while read -r line; do
        submodule=$(echo "$line" | cut -d' ' -f2)
        echo -e "${YELLOW}  Modified: $submodule${NC}"
        
        # Show the commit difference
        cd "$submodule"
        OLD_COMMIT=$(git rev-parse HEAD~1 2>/dev/null || echo "initial")
        NEW_COMMIT=$(git rev-parse HEAD)
        
        if [[ "$OLD_COMMIT" != "initial" ]]; then
            echo -e "${BLUE}    $OLD_COMMIT -> $NEW_COMMIT${NC}"
            
            # Show commit messages
            git log --oneline "$OLD_COMMIT..$NEW_COMMIT" | head -3 | while read -r commit_line; do
                echo -e "${BLUE}      $commit_line${NC}"
            done
        else
            echo -e "${BLUE}    New submodule at $NEW_COMMIT${NC}"
        fi
        
        cd - > /dev/null
        echo ""
    done
    
    return 1  # Changes exist
}

# Function to commit submodule updates
commit_updates() {
    echo -e "${BLUE}💾 Committing submodule updates...${NC}"
    
    # Add all submodule changes
    git add tools/
    
    # Create commit message with details
    COMMIT_MSG="Update submodules to latest main branches

Updated submodules:
$(git status --porcelain | grep "^M " | sed 's/^M  /- /')"
    
    git commit -m "$COMMIT_MSG"
    echo -e "${GREEN}✅ Submodule updates committed${NC}"
    echo ""
}

# Function to show final status
show_final_status() {
    echo -e "${BLUE}📋 Final submodule status:${NC}"
    git submodule status
    echo ""
    
    echo -e "${GREEN}🎉 Submodule update complete!${NC}"
    echo ""
    echo -e "${BLUE}💡 Next steps:${NC}"
    echo -e "${BLUE}  • Review the changes: git log -1${NC}"
    echo -e "${BLUE}  • Push to remote: git push${NC}"
    echo -e "${BLUE}  • Or undo changes: git reset --hard HEAD~1${NC}"
}

# Main execution
main() {
    check_root_directory
    show_submodule_status
    
    # Ask for confirmation
    echo -e "${YELLOW}This will:${NC}"
    echo -e "${YELLOW}  1. Update the main repository${NC}"
    echo -e "${YELLOW}  2. Update all submodules to latest main branch commits${NC}"
    echo -e "${YELLOW}  3. Commit the submodule updates${NC}"
    echo ""
    read -p "Continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}ℹ️  Update cancelled${NC}"
        exit 0
    fi
    
    echo ""
    
    update_main_repo
    update_submodules
    
    if show_changes; then
        echo -e "${GREEN}✅ All submodules are already up to date${NC}"
    else
        commit_updates
    fi
    
    show_final_status
}

# Handle script arguments
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --status       Show current submodule status only"
        echo "  --no-commit    Update submodules but don't commit changes"
        echo ""
        echo "This script updates all git submodules to the latest commits"
        echo "on their tracked branches and commits the changes."
        ;;
    "--status")
        check_root_directory
        show_submodule_status
        ;;
    "--no-commit")
        check_root_directory
        show_submodule_status
        update_main_repo
        update_submodules
        show_changes || true
        echo -e "${BLUE}ℹ️  Submodules updated but not committed${NC}"
        echo -e "${BLUE}Run 'git add tools/ && git commit' to commit changes${NC}"
        ;;
    "")
        main
        ;;
    *)
        echo -e "${RED}❌ Unknown option: $1${NC}"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
