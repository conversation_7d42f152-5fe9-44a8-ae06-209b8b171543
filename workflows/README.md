# Workflows

This directory contains documented workflows for common tasks.

## Purpose
- Store step-by-step procedures for recurring tasks
- Document efficient approaches discovered through experience
- Share reusable patterns across different projects

## Structure
- Each workflow should be a markdown file with clear steps
- Include examples and edge cases
- Reference any scripts or tools used

## Current Workflows
- **chrome_debug_mode.md** - How to start Chrome in debug mode for automation and testing
- **html_poster_creation.md** - Creating event posters using HTML/CSS with best practices
- **web_research_to_csv.md** - Converting web research into structured CSV data