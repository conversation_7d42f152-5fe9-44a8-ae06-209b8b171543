# Chrome Debug Mode Setup

## Purpose
Opening Chrome in debug mode allows for remote debugging, automation, and testing with tools like Puppeteer or Selenium.

## Command to Start Chrome in Debug Mode

### macOS
```bash
"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" --user-data-dir="/tmp/chrome-debug-profile" --remote-debugging-port=9222 --no-first-run --no-default-browser-check &
```

### Key Parameters
- `--user-data-dir="/tmp/chrome-debug-profile"`: Creates a temporary profile to avoid conflicts with existing Chrome sessions
- `--remote-debugging-port=9222`: Enables Chrome DevTools Protocol on port 9222
- `--no-first-run`: Skips first-run experience
- `--no-default-browser-check`: Prevents default browser prompt
- `&`: Runs the process in background

## Verification
Once started, Chrome will output:
```
DevTools listening on ws://127.0.0.1:9222/devtools/browser/[unique-id]
```

## Connecting to Debug Session
- Direct browser: Navigate to `http://localhost:9222`
- Puppeteer: `puppeteer.connect({ browserURL: 'http://localhost:9222' })`
- Selenium: Configure ChromeDriver with debugging port

## Stopping the Debug Session
```bash
# Find Chrome process
ps aux | grep "chrome-debug-profile"

# Kill the process
kill [PID]
```

## Notes
- The temporary profile ensures a clean, isolated Chrome instance
- Port 9222 is the standard debugging port but can be changed
- Some error messages about GCM registration are normal and can be ignored