# HTML Poster Creation Workflow

## Overview
Creating professional event posters using HTML/CSS for flexibility and easy customization.

## Advantages over Image-based Posters
- Easy text editing without regenerating
- Responsive design for different screen sizes
- Better accessibility
- Smaller file sizes
- Print-friendly CSS included
- Easy to convert to PDF using browser print

## Key Design Principles
1. **Multi-column layout**: More visually interesting than single column
2. **Clear visual hierarchy**: Header → Date → Content
3. **Consistent spacing**: Using CSS Grid and Flexbox
4. **Professional color scheme**: Primary blue gradient, accent red
5. **Placeholder approach**: Clearly marked image locations with dimensions

## Standard Structure
```
Header (with host logo)
├── Event Title
└── Host Logo Placeholder

Date/Time Banner

Main Content (Grid)
├── Left Column
│   ├── Speaker Section (photo + bio)
│   ├── Abstract
│   └── Tags
└── Right Column
    └── QR Code Section
```

## Image Requirements
- Host Logo: 200×80px
- Speaker Photo: 200×200px  
- QR Code: 250×250px

## Lessons Learned
- HTML posters are more flexible than static images
- Placeholders with dimensions help users prepare correct images
- Responsive design important for viewing on different devices
- Print CSS ensures good output when saving as PDF

## Tools Used
- Write: For creating HTML and documentation files
- Modern CSS features: Grid, Flexbox, custom properties
- Google Fonts for professional typography