# Web Research to CSV Workflow

## Overview
This workflow documents the process of researching companies/organizations online and storing the results in a CSV file.

## Steps

1. **Task Planning**
   - Create todo list with clear steps:
     - Search for target information
     - Collect relevant data
     - Create CSV file with results

2. **Research Phase**
   - Use WebSearch tool with specific keywords
   - Include location modifiers (e.g., "RTP", "Durham NC")
   - Search for multiple variations if initial results are limited

3. **Data Collection**
   - For each entity found:
     - Search for contact information (email, phone)
     - Note physical address
     - Capture brief description
   - If direct emails aren't available, note generic ones (support@, info@)

4. **CSV Creation**
   - Structure: Company Name, Email, Phone, Address, Description
   - Store in tmp/ directory for temporary files
   - Use clear, descriptive filename

## Lessons Learned
- Multiple searches often needed for complete contact info
- Some companies only provide partial email addresses in search results
- Official websites often have more complete contact information

## Example
Task: Find 5 ecommerce companies in RTP area
Result: `/tmp/rtp_ecommerce_companies.csv`

## Tools Used
- WebSearch: For finding companies and contact info
- Write: For creating CSV file
- TodoWrite/TodoRead: For task tracking